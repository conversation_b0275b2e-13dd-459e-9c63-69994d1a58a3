🚀 UI-Only Augment Plan for the Lead Generation Modal (Phases)
This plan assumes you're working in React (or similar frontend) and will test with static/mock data.

✅ Phase 1: Structure & Layout Refactor
Goal: Convert existing modal into a modern, clean, single-column layout.
the modal should take a decent width
Tasks:
Replace 2-column or mixed layout with single-column stacking.

SECTIONS:

1. Lead Information
Basic info about the potential customer.

Field	Notes
Customer Name	Required
Phone Number	Required, unique
Customer Category	Dropdown (Trader, SACCO, etc.)
ISIC Sector	Searchable dropdown
Lead Type (Existing/New)	Drives conditional logic
Client ID	Only visible if "Existing" is selected
2. Branch & Relationship Assignment
Details of which branch and officer is responsible.

Field	Notes
Branch Name	Dropdown
RM Code	Auto-filled from logged-in user
RM Name (optional)	Read-only or hidden

3. Contact Person Details
Only required if customer is not Employed, Youth, or Junior.

Field	Notes
Contact Person Name	Required based on customer category
Contact Person Phone No

Use a modal container with:

4. Employer Information
Only required if customer is Employed.

Field	Notes
Employer Name

max-height: 80vh

overflow-y: auto

Clean spacing using padding + vertical rhythm.

Output:
Reusable LeadFormModal.jsx or LeadModal.vue

Static layout with no interactivity yet

✅ Phase 2: Mock Data & UI Components
Goal: Replace dynamic values with test/mock data for dropdowns and inputs.

Tasks:
Populate dropdowns with mock data:

Customer Categories

ISIC sectors

Branches

Employer names

Simulate logged-in user for auto-filled RM Code (e.g., useMockSession()).

Create initial useState (or reactive variables) to bind form fields.

Output:
Fully testable mock-driven form

Field values stored in local component state

Sample mock console.log(formData) on submit

✅ Phase 3: Conditional Field Behavior
Goal: Show/hide fields based on form logic (lead type, category).

Tasks:
If "Lead Type" is "Existing", show Client ID.

If category is “Employed”, show Employer Name.

If category is NOT Employed, Youth, or Junior → require Contact Person fields.

Add real-time field validation:

Phone number format

Required fields (with stars or red borders)

Output:
Functional UI logic with rules

Disabled Submit if required fields missing

✅ Phase 4: UX Enhancements
Goal: Add clarity, polish, and accessibility.

Tasks:
Add tooltips or info icons for:

ISIC Sector (ℹ️: industry classification)

Client ID (ℹ️: only for existing customers)

RM Code (ℹ️: auto-filled based on user)

Add Submit, Reset, and Cancel buttons at bottom

Submit = logs form data

Reset = clears inputs

Cancel = closes modal

Add Submit Loading State to simulate processing

Output:
Smooth user flow for test inputs

UX-level validation with visual polish

✅ Phase 5: Final Review & Pre-Integration Prep
Goal: Ensure it's ready for backend connection later.

Tasks:
Ensure all form data is neatly stored in a single formData object.

Prepare a handleSubmit() method with structured output like:

js
Copy
Edit
{
  customer_name: 'Jane Doe',
  phone_number: '0712345678',
  lead_type: 'Existing',
  client_id: 'KB12345',
  branch: 'Westlands',
  ...
}
Comment placeholders where API calls will be added.

Create a README snippet explaining:

Required fields

Dynamic field behavior

Integration points
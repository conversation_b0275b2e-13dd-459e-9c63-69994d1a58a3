import { useState, useEffect, useCallback } from "react";
import { Phone } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import Modal from "../components/common/Modal";
import CallToDoForm from "../components/forms/CallToDoForm";
import { customerServiceService } from "../services/customerServiceService";
import { toast } from "react-toastify";

const CallsToDo = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showCallModal, setShowCallModal] = useState(false);
  const [callsToDoData, setCallsToDoData] = useState([]);
  
  // Filter state
  const [filters, setFilters] = useState({
    type: "",
  });

  // Filter configuration
  const filterConfig = [
    {
      key: "type",
      label: "Type",
      field: "hitlist_type",
      placeholder: "All Types",
      selectedValue: filters.type,
      options: [
        { value: "2by2by2", label: "2by2by2" },
        { value: "Dormancy", label: "Dormancy" },
      ],
    },
  ];

  // Handle filter changes
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  // Handle clearing filters
  const handleClearFilters = () => {
    setFilters({
      type: "",
    });
  };

  // Helper function to format phase names
  const formatPhase = (phase) => {
    switch (phase) {
      case "first2":
        return "First 2";
      case "second2":
        return "Second 2";
      case "third2":
        return "Third 2";
      default:
        return phase;
    }
  };

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  // Status badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = (status) => {
      switch (status?.toLowerCase()) {
        case "scheduled":
          return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
        case "pending":
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
        case "overdue":
          return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
        case "completed":
          return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      }
    };

    return (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
          status
        )}`}
      >
        {status || "-"}
      </span>
    );
  };

  // Customer hover tooltip component
  const CustomerCell = ({ customer_name, account_number, phone_number }) => {
    return (
      <div className="group relative">
        <div className="cursor-pointer">
          <div className="font-medium text-gray-900 dark:text-white">
            {customer_name}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {account_number}
          </div>
        </div>

        {/* Hover tooltip */}
        <div className="absolute left-0 top-full mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
          <div className="space-y-2">
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Customer:
              </span>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {customer_name}
              </div>
            </div>
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Account Number:
              </span>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {account_number}
              </div>
            </div>
            <div>
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Phone Number:
              </span>
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {phone_number}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Define columns for the table
  const columns = [
    {
      key: "customer",
      title: "CUSTOMER",
      render: (value, row) => (
        <CustomerCell
          customer_name={row.customer_name}
          account_number={row.account_number}
          phone_number={row.phone_number}
        />
      ),
    },
    {
      key: "phone_number",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "hitlist_type",
      title: "HITLIST TYPE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "phase_step",
      title: "PHASE / STEP",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "-"}
        </span>
      ),
    },
    {
      key: "scheduled",
      title: "SCHEDULED",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(value)}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => <StatusBadge status={value} />,
    },
    {
      key: "action",
      title: "ACTION",
      render: (value, row) => (
        <button
          onClick={() => handleCallNow(row)}
          className="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-lg transition-colors duration-200"
        >
          <Phone size={12} className="mr-1" />
          Call Now
        </button>
      ),
    },
  ];

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more calls to do");
    }, 2000);
  };

  const handleCallNow = (item) => {
    setSelectedItem(item);
    setShowCallModal(true);
    console.log("Initiating call for:", item.customer_name);
  };

  // Fetch calls to do data (moved from useEffect for reuse)
  const fetchCallsToDo = useCallback(async () => {
    try {
      setLoading(true);
      const data = await customerServiceService.getCallsToDo();
      
      // Transform API data to match table format
      const transformedData = data.map((item, index) => ({
        id: item.id,
        uniqueKey: `${item.id}-${index}`, // Add unique key for React rendering
        customer_name: item.customerName,
        account_number: item.accountNumber,
        phone_number: item.phoneNumber,
        hitlist_type: item.hitlistType,
        phase_step: item.phase ? formatPhase(item.phase) : "-",
        scheduled: item.scheduled,
        status: item.status?.value || "Pending",
        // Keep original data for form
        hitlistType: item.hitlistType,
        phase: item.phase,
      }));
      
      setCallsToDoData(transformedData);
    } catch (error) {
      console.error("Error fetching calls to do:", error);
      setCallsToDoData([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch calls to do data on component mount
  useEffect(() => {
    fetchCallsToDo();
  }, [fetchCallsToDo]);

  const handleCallSubmit = async (callData, customer, error) => {
    console.log("Call submitted:", { callData, customer, error });

    if (!error && callData && customer) {
      console.log("Call created successfully");
      // Show success message
      toast.success("Call recorded successfully");
      // Update the state locally instead of refetching
      setCallsToDoData(prevData => 
        prevData.map(item => 
          item.id === customer.id 
            ? { ...item, status: "Completed" }
            : item
        )
      );
    } else if (error) {
      // Show error message
      toast.error(error.response?.data?.message || "Failed to record call");
    }

    setShowCallModal(false);
    setSelectedItem(null);
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting calls to do data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing calls to do data");
    // Print functionality here
  };

  return (
    <PrivateLayout pageTitle="Calls To Do">
      <div className="space-y-6">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={callsToDoData.filter((item) => {
            // Type filter
            if (filters.type && item.hitlist_type !== filters.type) {
              return false;
            }
            return true;
          })}
          searchPlaceholder="Search calls to do..."
          onView={handleView}
          actions={[]} // Hide default actions column
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="calls to do"
          showDataCount={true}
          highlightField="status"
          highlightColors={{
            Scheduled:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Overdue:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Export functionality - only export and print
          showImportExport={true}
          showImport={false}
          onExport={handleExport}
          onPrint={handlePrint}
          // Filter functionality
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />

        {/* Call Modal */}
        {showCallModal && selectedItem && (
          <Modal
            isOpen={showCallModal}
            onClose={() => {
              setShowCallModal(false);
              setSelectedItem(null);
            }}
            title={`Make Call - ${selectedItem?.customer_name}`}
            size="lg"
          >
            <CallToDoForm
              item={selectedItem}
              onClose={() => {
                setShowCallModal(false);
                setSelectedItem(null);
              }}
              onSubmit={handleCallSubmit}
            />
          </Modal>
        )}
      </div>
    </PrivateLayout>
  );
};

export default CallsToDo;

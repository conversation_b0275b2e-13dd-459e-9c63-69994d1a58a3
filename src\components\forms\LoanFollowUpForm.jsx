import { useState } from "react";
import { X, Loader2 } from "lucide-react";

const LoanFollowUpForm = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    customerName: "",
    loanId: "",
    followUpType: "",
    followUpReason: "",
    scheduledDate: "",
    scheduledTime: "",
    assignedOfficer: "",
    notes: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = "Customer name is required";
    }

    if (!formData.loanId.trim()) {
      newErrors.loanId = "Loan ID is required";
    }

    if (!formData.followUpType.trim()) {
      newErrors.followUpType = "Follow-up type is required";
    }

    if (!formData.followUpReason.trim()) {
      newErrors.followUpReason = "Follow-up reason is required";
    }

    if (!formData.scheduledDate.trim()) {
      newErrors.scheduledDate = "Scheduled date is required";
    }

    if (!formData.scheduledTime.trim()) {
      newErrors.scheduledTime = "Scheduled time is required";
    }

    if (!formData.assignedOfficer.trim()) {
      newErrors.assignedOfficer = "Assigned officer is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const submitData = {
        ...formData,
        scheduledDateTime: `${formData.scheduledDate}T${formData.scheduledTime}`,
      };

      const success = await onSubmit(submitData);
      if (success) {
        onClose();
      }
    } catch (error) {
      console.error("Error submitting loan follow-up form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reset
  const handleReset = () => {
    setFormData({
      customerName: "",
      loanId: "",
      followUpType: "",
      followUpReason: "",
      scheduledDate: "",
      scheduledTime: "",
      assignedOfficer: "",
      notes: "",
    });
    setErrors({});
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Create New Loan Follow-up
        </h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <X size={24} />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Customer Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Customer & Loan Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Customer Name *
              </label>
              <input
                type="text"
                name="customerName"
                value={formData.customerName}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.customerName ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter customer name"
              />
              {errors.customerName && (
                <p className="mt-1 text-sm text-red-600">{errors.customerName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Loan ID *
              </label>
              <input
                type="text"
                name="loanId"
                value={formData.loanId}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.loanId ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter loan ID"
              />
              {errors.loanId && (
                <p className="mt-1 text-sm text-red-600">{errors.loanId}</p>
              )}
            </div>
          </div>
        </div>

        {/* Follow-up Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Follow-up Details
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Follow-up Type *
              </label>
              <select
                name="followUpType"
                value={formData.followUpType}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.followUpType ? "border-red-500" : "border-gray-300"
                }`}
              >
                <option value="">Select follow-up type</option>
                <option value="phone-call">Phone Call</option>
                <option value="site-visit">Site Visit</option>
                <option value="office-visit">Office Visit</option>
                <option value="email">Email</option>
                <option value="sms">SMS</option>
              </select>
              {errors.followUpType && (
                <p className="mt-1 text-sm text-red-600">{errors.followUpType}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Follow-up Reason *
              </label>
              <select
                name="followUpReason"
                value={formData.followUpReason}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.followUpReason ? "border-red-500" : "border-gray-300"
                }`}
              >
                <option value="">Select follow-up reason</option>
                <option value="payment-reminder">Payment Reminder</option>
                <option value="loan-review">Loan Review</option>
                <option value="documentation">Documentation</option>
                <option value="customer-inquiry">Customer Inquiry</option>
                <option value="loan-restructuring">Loan Restructuring</option>
                <option value="collection">Collection</option>
                <option value="relationship-management">Relationship Management</option>
              </select>
              {errors.followUpReason && (
                <p className="mt-1 text-sm text-red-600">{errors.followUpReason}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Scheduled Date *
              </label>
              <input
                type="date"
                name="scheduledDate"
                value={formData.scheduledDate}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.scheduledDate ? "border-red-500" : "border-gray-300"
                }`}
                min={new Date().toISOString().split('T')[0]}
              />
              {errors.scheduledDate && (
                <p className="mt-1 text-sm text-red-600">{errors.scheduledDate}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Scheduled Time *
              </label>
              <input
                type="time"
                name="scheduledTime"
                value={formData.scheduledTime}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.scheduledTime ? "border-red-500" : "border-gray-300"
                }`}
              />
              {errors.scheduledTime && (
                <p className="mt-1 text-sm text-red-600">{errors.scheduledTime}</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Assigned Officer *
            </label>
            <input
              type="text"
              name="assignedOfficer"
              value={formData.assignedOfficer}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                errors.assignedOfficer ? "border-red-500" : "border-gray-300"
              }`}
              placeholder="Enter assigned officer name"
            />
            {errors.assignedOfficer && (
              <p className="mt-1 text-sm text-red-600">{errors.assignedOfficer}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="Additional notes for the follow-up..."
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Reset
          </button>
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Follow-up"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LoanFollowUpForm;

import * as XLSX from 'xlsx';

// Lead template fields
export const LEAD_TEMPLATE_FIELDS = [
  { key: 'Lead Name', label: 'Lead Name', required: true },
  { key: 'Anchor', label: 'Anchor ', required: false },
  { key: 'PhoneNumber', label: 'Phone Number', required: true },
  { key: 'RM CODE', label: 'RM CODE', required: false },
  { key: 'Category', label: 'Category', required: false },
  { key: 'ISIC SECTOR', label: 'ISIC SECTOR', required: false },
  { key: 'Lead Type', label: 'Lead Type', required: false },
  { key: 'Client ID', label: 'Client ID', required: false },
  { key: 'Branch ', label: 'Branch ', required: false },
  { key: 'Contact Person ', label: 'Contact Person', required: false },
  { key: 'Contact  Phone', label: 'Contact Phone', required: false },
  { key: 'Employer', label: 'Employer', required: false },
];

// Loan customer template fields (same as leads but with 'lead' changed to 'customer')
export const LOA<PERSON>_CUSTOMER_TEMPLATE_FIELDS = [
  { key: 'Customer Name', label: 'Customer Name', required: true },
  { key: 'Anchor', label: 'Anchor ', required: false },
  { key: 'PhoneNumber', label: 'Phone Number', required: true },
  { key: 'RM CODE', label: 'RM CODE', required: false },
  { key: 'Category', label: 'Category', required: false },
  { key: 'ISIC SECTOR', label: 'ISIC SECTOR', required: false },
  { key: 'Customer Type', label: 'Customer Type', required: false },
  { key: 'Client ID', label: 'Client ID', required: false },
  { key: 'Branch ', label: 'Branch ', required: false },
  { key: 'Contact Person ', label: 'Contact Person', required: false },
  { key: 'Contact  Phone', label: 'Contact Phone', required: false },
  { key: 'Employer', label: 'Employer', required: false },
];



/**
 * Generate and download Excel template for leads import
 */
export const downloadLeadsTemplate = () => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Create template sheet with just headers
    const headers = LEAD_TEMPLATE_FIELDS.map(field => field.label);

    // Create template data with just headers (no instructions or sample data)
    const templateData = [headers];

    const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

    // Set column widths
    const columnWidths = LEAD_TEMPLATE_FIELDS.map(() => ({ wch: 20 }));
    templateSheet['!cols'] = columnWidths;

    // Style the header row (row 0)
    const headerRange = XLSX.utils.decode_range(templateSheet['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (!templateSheet[cellAddress]) continue;
      templateSheet[cellAddress].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4CAF50" } },
        border: {
          top: { style: "thin" },
          bottom: { style: "thin" },
          left: { style: "thin" },
          right: { style: "thin" }
        },
        alignment: { horizontal: "center" }
      };
    }

    XLSX.utils.book_append_sheet(workbook, templateSheet, 'Leads Import Template');

    // Generate and download the file
    const fileName = `Leads-Import-Template-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    

    return true;
  } catch (error) {
    console.error('Error generating template:', error);
    throw new Error('Failed to generate template file');
  }
};

/**
 * Generate and download Excel template for loan customers import
 */
export const downloadLoanCustomersTemplate = () => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Create template sheet with just headers
    const headers = LOAN_CUSTOMER_TEMPLATE_FIELDS.map(field => field.label);

    // Create template data with just headers (no instructions or sample data)
    const templateData = [headers];

    const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

    // Set column widths
    const columnWidths = LOAN_CUSTOMER_TEMPLATE_FIELDS.map(() => ({ wch: 20 }));
    templateSheet['!cols'] = columnWidths;

    // Style the header row (row 0)
    const headerRange = XLSX.utils.decode_range(templateSheet['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (!templateSheet[cellAddress]) continue;
      templateSheet[cellAddress].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "4CAF50" } },
        border: {
          top: { style: "thin" },
          bottom: { style: "thin" },
          left: { style: "thin" },
          right: { style: "thin" }
        },
        alignment: { horizontal: "center" }
      };
    }

    XLSX.utils.book_append_sheet(workbook, templateSheet, 'Loan Customers Import Template');

    // Generate and download the file
    const fileName = `Loan-Customers-Import-Template-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    return true;
  } catch (error) {
    console.error('Error generating loan customers template:', error);
    throw new Error('Failed to generate loan customers template file');
  }
};

// Note: File parsing is now handled by the backend.
// This file contains template generation and export functionality.

/**
 * Export leads data to Excel file
 * @param {string} searchQuery - Optional search query for filtering
 * @param {Function} onProgress - Progress callback function
 * @returns {Promise<boolean>} - Success status
 */
export const exportLeadsToExcel = async (searchQuery = '', onProgress = null) => {
  try {
    // Show initial progress
    if (onProgress) onProgress(0, 'Preparing export...');

    // Import the leads service dynamically to avoid circular imports
    const { leadsService } = await import('../services/leadsService');

    // Show progress for API call
    if (onProgress) onProgress(20, 'Requesting data from server...');

    // Call the export API
    const response = await leadsService.exportToExcel(searchQuery, onProgress);

    // Show progress for file processing
    if (onProgress) onProgress(80, 'Processing file...');

    // Create blob from response
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // Extract filename from Content-Disposition header or use default
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'leads-export.xlsx';

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    }

    // Show final progress
    if (onProgress) onProgress(90, 'Downloading file...');

    // Create download link and trigger download
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    // Complete progress
    if (onProgress) onProgress(100, 'Export completed!');

    // Store export history
    storeExportHistory(filename, searchQuery);

    return true;
  } catch (error) {
    console.error('Error exporting leads:', error);
    throw new Error(error.response?.data?.message || 'Failed to export leads');
  }
};

/**
 * Store export history in localStorage
 * @param {string} filename - Downloaded filename
 * @param {string} searchQuery - Search query used
 */
const storeExportHistory = (filename, searchQuery) => {
  try {
    const history = getExportHistory();
    const newEntry = {
      id: Date.now(),
      filename,
      searchQuery,
      timestamp: new Date().toISOString(),
      size: null // Will be populated if we can get file size
    };

    // Add to beginning of array and limit to 10 entries
    history.unshift(newEntry);
    const limitedHistory = history.slice(0, 10);

    localStorage.setItem('leads_export_history', JSON.stringify(limitedHistory));
  } catch (error) {
    console.warn('Failed to store export history:', error);
  }
};

/**
 * Get export history from localStorage
 * @returns {Array} - Array of export history entries
 */
export const getExportHistory = () => {
  try {
    const history = localStorage.getItem('leads_export_history');
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.warn('Failed to get export history:', error);
    return [];
  }
};

/**
 * Clear export history
 */
export const clearExportHistory = () => {
  try {
    localStorage.removeItem('leads_export_history');
  } catch (error) {
    console.warn('Failed to clear export history:', error);
  }
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (!bytes) return 'Unknown size';

  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

/**
 * Get export format options
 * @returns {Array} - Available export formats
 */
export const getExportFormats = () => [
  { value: 'xlsx', label: 'Excel (.xlsx)', icon: '📊' },
  // { value: 'csv', label: 'CSV (.csv)', icon: '📄' },
  // { value: 'pdf', label: 'PDF (.pdf)', icon: '📋' }
];

import React, { useState, useRef, useEffect } from "react";
import Chart from "react-apexcharts";
import useCountUp from "../../hooks/useCountUp";

function StaffCallsChart() {
  // Test data - array of staff with calls made vs target
  const staffData = [
    { name: "<PERSON>", callsMade: 85, target: 100 },
    { name: "<PERSON>", callsMade: 92, target: 100 },
    { name: "<PERSON>", callsMade: 78, target: 100 },
    { name: "<PERSON>", callsMade: 105, target: 100 },
    { name: "<PERSON>", callsMade: 88, target: 100 },
    { name: "<PERSON>", callsMade: 96, target: 100 },
    { name: "<PERSON>", callsMade: 73, target: 100 },
    { name: "<PERSON>", callsMade: 110, target: 100 },
    { name: "<PERSON>", callsMade: 82, target: 100 },
    { name: "<PERSON>", callsMade: 94, target: 100 },
    { name: "<PERSON>", callsMade: 87, target: 100 },
    { name: "<PERSON>", callsMade: 99, target: 100 },
    { name: "<PERSON>", callsMade: 76, target: 100 },
    { name: "<PERSON>", callsMade: 103, target: 100 },
    { name: "<PERSON>", callsMade: 91, target: 100 },
    { name: "<PERSON>", calls<PERSON><PERSON>: 89, target: 100 },
    { name: "<PERSON>", calls<PERSON><PERSON>: 84, target: 100 },
    { name: "<PERSON> <PERSON>", calls<PERSON><PERSON>: 97, target: 100 },
    { name: "<PERSON> <PERSON>", calls<PERSON><PERSON>: 79, target: 100 },
    { name: "<PERSON> <PERSON>", calls<PERSON>ade: 108, target: 100 },
    { name: "<PERSON> <PERSON>", calls<PERSON><PERSON>: 86, target: 100 },
    { name: "<PERSON> <PERSON>", calls<PERSON><PERSON>: 93, target: 100 },
    { name: "Ryan Jackson", callsMade: 81, target: 100 },
    { name: "Stephanie Harris", callsMade: 95, target: 100 },
    { name: "Brandon Lewis", callsMade: 77, target: 100 },
  ];

  // State for selected user
  const [selectedUser, setSelectedUser] = useState(0);

  // Animation state and ref
  const [hasAnimated, setHasAnimated] = useState(false);
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const chartRef = useRef(null);

  // Get selected user data
  const selectedUserData = staffData[selectedUser];

  // Intersection Observer for animation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setShouldAnimate(true);
            setHasAnimated(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (chartRef.current) {
      observer.observe(chartRef.current);
    }

    return () => {
      if (chartRef.current) {
        observer.unobserve(chartRef.current);
      }
    };
  }, [hasAnimated]);

  // Extract data for chart - single user
  const categories = [selectedUserData.name];
  const callsMadeData = [selectedUserData.callsMade];
  const targetData = [selectedUserData.target];

  // Animated values for summary stats
  const totalCallsMade = staffData.reduce(
    (sum, staff) => sum + staff.callsMade,
    0
  );
  const totalTarget = staffData.reduce((sum, staff) => sum + staff.target, 0);
  const animatedCallsMade = useCountUp(
    shouldAnimate ? totalCallsMade : 0,
    2000,
    0
  );
  const animatedTarget = useCountUp(shouldAnimate ? totalTarget : 0, 2000, 200);

  const chartOptions = {
    chart: {
      type: "bar",
      height: 450,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: shouldAnimate,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      background: "transparent",
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "55%",
        endingShape: "flat",
        borderRadius: 0,
        dataLabels: {
          position: "top",
        },
        colors: {
          backgroundBarColors: [],
          backgroundBarOpacity: 0,
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: false,
      width: 0,
    },
    xaxis: {
      categories: categories,
      labels: {
        rotate: -60,
        style: {
          fontSize: "16px",
          fontWeight: 600,
          colors: ["#374151"],
        },
        maxHeight: 140,
        trim: false,
        hideOverlappingLabels: false,
      },
      axisBorder: {
        show: true,
        color: "#e0e0e0",
      },
      axisTicks: {
        show: true,
        color: "#e0e0e0",
      },
    },
    yaxis: {
      title: {
        text: "Number of Calls",
        style: {
          fontSize: "14px",
          fontWeight: 600,
          color: "#374151",
        },
      },
      labels: {
        style: {
          fontSize: "12px",
          colors: ["#6b7280"],
        },
      },
    },
    fill: {
      opacity: 1,
      type: "solid",
    },
    colors: ["#FDCA37", "#3589FD"], // Gold for calls made, Blue for targets
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
      active: {
        allowMultipleDataPointsSelection: false,
        filter: {
          type: "none",
        },
      },
    },
    legend: {
      show: false,
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter: function (val, { seriesIndex, dataPointIndex }) {
          const staff = staffData[dataPointIndex];
          if (seriesIndex === 0) {
            const percentage = ((val / staff.target) * 100).toFixed(1);
            return `${val} calls (${percentage}% of target)`;
          }
          return `${val} calls (target)`;
        },
      },
      style: {
        fontSize: "12px",
      },
    },
    grid: {
      borderColor: "#e5e7eb",
      strokeDashArray: 0,
      xaxis: {
        lines: {
          show: false,
        },
      },
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          chart: {
            height: 400,
          },
          plotOptions: {
            bar: {
              columnWidth: "85%",
              endingShape: "flat",
              borderRadius: 0,
            },
          },
          xaxis: {
            labels: {
              rotate: -75,
              style: {
                fontSize: "14px",
                fontWeight: 600,
              },
              maxHeight: 120,
              trim: false,
              hideOverlappingLabels: false,
            },
          },
          dataLabels: {
            style: {
              fontSize: "11px",
            },
          },
        },
      },
    ],
  };

  const series = [
    {
      name: "Calls Made",
      data: callsMadeData,
      color: "#82C355",
    },
    {
      name: "Target",
      data: targetData,
      color: "#1c5b41",
    },
  ];

  return (
    <div ref={chartRef} className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-[23px] font-semibold">
            Monthly Calls Made vs Targets
          </h2>
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(parseInt(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {staffData.map((staff, index) => (
              <option key={index} value={index}>
                {staff.name}
              </option>
            ))}
          </select>
        </div>

        {/* Custom Legend */}
        <div className="flex justify-center items-center gap-8 mb-4">
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-sm transition-colors duration-200 hover:opacity-80"
              style={{ backgroundColor: "#82C355" }}
              onMouseEnter={(e) => (e.target.style.backgroundColor = "#6BA043")}
              onMouseLeave={(e) => (e.target.style.backgroundColor = "#82C355")}
            ></div>
            <span className="text-sm font-medium text-gray-700">
              Calls Made
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-sm transition-colors duration-200 hover:opacity-80"
              style={{ backgroundColor: "#1c5b41" }}
              onMouseEnter={(e) => (e.target.style.backgroundColor = "#164832")}
              onMouseLeave={(e) => (e.target.style.backgroundColor = "#1c5b41")}
            ></div>
            <span className="text-sm font-medium text-gray-700">Target</span>
          </div>
        </div>
      </div>

      {/* Chart container for single user */}
      <div className="w-full">
        <Chart options={chartOptions} series={series} type="bar" height={450} />
      </div>

      {/* Summary stats */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold" style={{ color: "#82C355" }}>
            {animatedCallsMade.toLocaleString()}
          </div>
          <div className="text-sm text-gray-600">Total Calls Made</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold" style={{ color: "#1c5b41" }}>
            {animatedTarget.toLocaleString()}
          </div>
          <div className="text-sm text-gray-600">Total Target</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {(
              (staffData.reduce((sum, staff) => sum + staff.callsMade, 0) /
                staffData.reduce((sum, staff) => sum + staff.target, 0)) *
              100
            ).toFixed(1)}
            %
          </div>
          <div className="text-sm text-gray-600">Overall Achievement</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {
              staffData.filter((staff) => staff.callsMade >= staff.target)
                .length
            }
          </div>
          <div className="text-sm text-gray-600">Staff Meeting Target</div>
        </div>
      </div>
    </div>
  );
}

export default StaffCallsChart;

import { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import BranchForm from "../components/forms/BranchForm";
import { useApi } from "../contexts/ApiContext";
import { usePagination } from "../hooks/usePagination";
import { toast } from "react-toastify";

const Branches = () => {
    const { branchApi } = useApi();

    // Pagination hook with backend integration
    const {
        data: branches,
        loading,
        currentPage,
        totalPages,
        totalItems,
        pageSize,
        goToPage,
        changePageSize,
        refresh,
    } = usePagination({
        fetchData: useCallback(async (params) => {
            console.log('Fetching branches with params:', params);
            return await branchApi.getAll(params);
        }, [branchApi]),
        initialPageSize: 10,
        autoFetch: true,
    });

    // Define columns of the table here and what to render
    const columns = [

        {
            key: "name",
            title: "BRANCH",
            render: (value) => (
                <span className="font-medium text-gray-900 dark:text-white">
                    {value}
                </span>
            ),
        },

        {
            key: "region",
            title: "REGION",
            render: (value) => {
                const getRegionColor = (region) => {
                    switch (region) {
                        case "NAIROBI":
                            return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
                        case "CENTRAL":
                            return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
                        default:
                            return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
                    }
                };

                return (
                    <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRegionColor(
                            value
                        )}`}
                    >
                        {value}
                    </span>
                );
            },
        },
        {
            key: "addedBy",
            title: "ADDED BY",
            render: (value) => (
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                    {value}
                </span>
            ),
        },

        {
            key: "addedOn",
            title: "ADDED ON",
            render: (value) => (
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    {value}
                </span>
            ),
        },

    ];

    // Helper function to trigger refresh
    const refreshData = () => {
        refresh();
    };

    // Form submission handlers
    const handleCreateSubmit = async (formData) => {
        try {
            await branchApi.create(formData);
            console.log("Created branch:", formData);
            toast.success("Branch created successfully!");
            // Refresh the data after successful creation
            refreshData();
        } catch (error) {
            console.error("Error creating branch:", error);
            toast.error(error.message || "Failed to create branch");
        }
    };

    const handleEditSubmit = async (formData, originalItem) => {
        try {
            await branchApi.update(originalItem.id, formData);
            console.log("Updated branch:", formData);
            toast.success("Branch updated successfully!");
            // Refresh the data after successful update
            refreshData();
        } catch (error) {
            console.error("Error updating branch:", error);
            toast.error(error.message || "Failed to update branch");
        }
    };

    const handleDeleteConfirm = async (branch) => {
        try {
            await branchApi.delete(branch.id);
            console.log("Deleted branch:", branch);
            toast.success("Branch deleted successfully!");
            // Refresh the data after successful deletion
            refreshData();
        } catch (error) {
            console.error("Error deleting branch:", error);
            toast.error(error.message || "Failed to delete branch");
        }
    };

    const handleView = (branch) => {
        console.log("View branch:", branch);
        // Here you would typically navigate to view page or show view modal
    };

    // Pagination handlers
    const handlePageChange = (page) => {
        goToPage(page);
    };

    const handlePageSizeChange = (newPageSize) => {
        changePageSize(newPageSize);
    };


    return (
        <PrivateLayout>
            {/* Data Table */}
            <div className="">
                <DataTable
                    columns={columns}
                    data={branches}
                    searchPlaceholder="Search branches..."
                    addButtonText="New Branch"
                    onView={handleView}
                    actions={["view", "edit", "delete"]}
                    loading={loading}
                    // Pagination props
                    showPagination={true}
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={totalItems}
                    pageSize={pageSize}
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    pageSizeOptions={[10, 20, 50, 100]}
                    showPageSizeSelector={true}
                    showItemsInfo={true}
                    showFirstLast={true}
                    maxVisiblePages={5}
                    highlightField="addedBy"
                    highlightColors={{
                        BUSINESS:
                            "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
                        PERSONAL:
                            "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
                    }}
                    // Modal forms
                    createForm={({ onClose }) => (
                        <BranchForm onClose={onClose} onSubmit={handleCreateSubmit} />
                    )}
                    editForm={({ item, onClose }) => (
                        <BranchForm
                            item={item}
                            onClose={onClose}
                            onSubmit={handleEditSubmit}
                        />
                    )}
                    deleteForm={({ item, onClose }) => (
                        <DeleteConfirmation
                            item={item}
                            onClose={onClose}
                            onConfirm={handleDeleteConfirm}
                            itemName="Branch"
                        />
                    )}
                    createModalTitle="Create New Branch"
                    editModalTitle="Branch"
                    deleteModalTitle=""
                    modalSize="lg"
                    deleteModalSize="sm"
                />

            </div>

        </PrivateLayout>

    )
}

export default Branches

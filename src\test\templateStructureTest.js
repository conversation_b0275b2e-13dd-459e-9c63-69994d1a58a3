// Test to verify template structure
import * as XLSX from 'xlsx';

// Lead template fields (copied from excelUtils.js)
const LEAD_TEMPLATE_FIELDS = [
  { key: 'customerName', label: 'Customer Full Name *', required: true },
  { key: 'phoneNumber', label: 'Phone Number * (07xxxxxxxx)', required: true },
  { key: 'customerCategory', label: 'Customer Category (Employed/Self Employed/etc)', required: false },
  { key: 'isicSector', label: 'Industry Sector (ISIC)', required: false },
  { key: 'leadType', label: 'Lead Type (New/Existing)', required: false },
  { key: 'clientId', label: 'Client ID (Required for Existing)', required: false },
  { key: 'branchName', label: 'Branch Name', required: false },
  { key: 'contactPersonName', label: 'Contact Person Name', required: false },
  { key: 'contactPersonPhone', label: 'Contact Person Phone', required: false },
  { key: 'employerName', label: 'Employer/Company Name', required: false },
];

// Sample data
const SAMPLE_LEAD_DATA = [
  {
    customerName: '<PERSON>',
    phoneNumber: '0712345678',
    customerCategory: 'Employed',
    isicSector: 'Manufacturing',
    leadType: 'New',
    clientId: '',
    branchName: 'Nairobi Main Branch',
    contactPersonName: 'Jane Wanjiku',
    contactPersonPhone: '0712345679',
    employerName: 'ABC Manufacturing Ltd',
  }
];

function testTemplateStructure() {
  console.log('Testing template structure...');
  
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Create template sheet with headers and sample data
    const headers = LEAD_TEMPLATE_FIELDS.map(field => field.label);
    
    // Add instruction row at the top
    const instructionRow = [
      'LEADS IMPORT TEMPLATE - Fill in your lead data below. Required fields marked with (*). Delete sample data before importing.',
      '', '', '', '', '', '', '', '', ''
    ];
    
    // Add notes row
    const notesRow = [
      'NOTES: Phone format 07xxxxxxxx or 01xxxxxxxx | Lead Type: New or Existing | Client ID required for Existing leads | Max 1000 leads per import',
      '', '', '', '', '', '', '', '', ''
    ];
    
    // Create template data with instruction rows, headers, and sample data
    const templateData = [
      instructionRow,
      notesRow,
      [], // Empty row for spacing
      headers, 
      ...SAMPLE_LEAD_DATA.map(row => 
        LEAD_TEMPLATE_FIELDS.map(field => row[field.key] || '')
      )
    ];

    const templateSheet = XLSX.utils.aoa_to_sheet(templateData);
    XLSX.utils.book_append_sheet(workbook, templateSheet, 'Leads Import Template');

    // Convert back to JSON to test parsing
    const jsonData = XLSX.utils.sheet_to_json(templateSheet, { header: 1 });
    
    console.log('Template structure:');
    jsonData.forEach((row, index) => {
      console.log(`Row ${index}:`, row[0] || '[empty]');
    });
    
    // Test header detection
    let headerRowIndex = -1;
    for (let i = 0; i < jsonData.length; i++) {
      if (jsonData[i] && jsonData[i][0]) {
        const firstCell = jsonData[i][0].toString().toLowerCase();
        console.log(`Testing row ${i} first cell:`, firstCell);
        // Look for customer name variations in the first column
        if (firstCell.includes('customer') && (firstCell.includes('name') || firstCell.includes('full'))) {
          headerRowIndex = i;
          console.log('✅ Found header row at index:', i);
          break;
        }
      }
    }
    
    if (headerRowIndex === -1) {
      console.error('❌ Could not find header row');
      return false;
    }
    
    console.log('✅ Template structure test passed');
    return true;
    
  } catch (error) {
    console.error('❌ Template structure test failed:', error);
    return false;
  }
}

// Run the test
testTemplateStructure();

export { testTemplateStructure };

import { useState, useEffect } from "react";
import { Phone, PhoneMissed, Mic, <PERSON><PERSON><PERSON><PERSON>, Pause, Play } from "lucide-react";
import Select from "react-select";

//NOTE: if an item is passed in, it's an edit form
const ActivityForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    clientId: "",
    clientName: "",
    anchorName: "",
    activityType: "call", // Default to call
    purpose: "",
    date: "",
    commentOnOutcome: "",
    nextVisitScheduleDate: "",
    status: "",
    businessSegment: "",
    activityBy: "Current User", // Auto-filled, uneditable
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Call functionality states
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callTimer, setCallTimer] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isOnHold, setIsOnHold] = useState(false);

  // Client ID options for React Select
  const clientIdOptions = [
    { value: "CL001", label: "CL001" },
    { value: "CL002", label: "CL002" },
    { value: "CL003", label: "CL003" },
    { value: "CL004", label: "CL004" },
    { value: "CL005", label: "CL005" },
    { value: "CL006", label: "CL006" },
    { value: "CL007", label: "CL007" },
    { value: "CL008", label: "CL008" },
    { value: "CL009", label: "CL009" },
    { value: "CL010", label: "CL010" },
  ];

  // Purpose options for React Select
  const purposeOptions = [
    { value: "Business Consultation", label: "Business Consultation" },
    {
      value: "Loan Application Follow-up",
      label: "Loan Application Follow-up",
    },
    { value: "Account Opening", label: "Account Opening" },
    { value: "Investment Advisory", label: "Investment Advisory" },
    { value: "Insurance Inquiry", label: "Insurance Inquiry" },
    { value: "Property Valuation", label: "Property Valuation" },
    { value: "Mortgage Application", label: "Mortgage Application" },
    { value: "Business Assessment", label: "Business Assessment" },
    { value: "Financial Planning", label: "Financial Planning" },
    { value: "Loan Disbursement", label: "Loan Disbursement" },
    { value: "Customer Onboarding", label: "Customer Onboarding" },
    { value: "Complaint Resolution", label: "Complaint Resolution" },
  ];

  // Status options for React Select
  const statusOptions = [
    { value: "Successful & warm", label: "Successful & warm" },
    { value: "Cold", label: "Cold" },
    { value: "Failed", label: "Failed" },
    { value: "Rescheduled", label: "Rescheduled" },
    { value: "Customer not interested", label: "Customer not interested" },
  ];

  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused
        ? "#10b981"
        : errors[state.selectProps.name]
        ? "#ef4444"
        : "#d1d5db",
      boxShadow: state.isFocused ? "0 0 0 1px #10b981" : "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
      "&:hover": {
        backgroundColor: state.isSelected ? "#10b981" : "#f3f4f6",
      },
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  // Initialize form data if editing
  useEffect(() => {
    if (item) {
      setFormData({
        clientId: item.clientId || "",
        clientName: item.clientName || "",
        anchorName: item.anchorName || "",
        activityType: item.activityType || "call",
        purpose: item.purpose || "",
        date: item.date ? item.date.split("T")[0] : "",
        commentOnOutcome: item.commentOnOutcome || "",
        nextVisitScheduleDate: item.nextVisitScheduleDate
          ? item.nextVisitScheduleDate.split("T")[0]
          : "",
        status: item.status || "",
        businessSegment: item.businessSegment || "",
        activityBy: item.activityBy || "Current User",
      });
    }
  }, [item]);

  // Handle regular input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle checkbox change for activity type
  const handleActivityTypeChange = (type) => {
    setFormData((prev) => ({
      ...prev,
      activityType: type,
    }));

    // Reset call states when switching activity type
    if (type !== "call") {
      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    }
  };

  // Call functionality handlers
  const handleStartCall = async () => {
    setIsConnecting(true);

    try {
      // Simulate API call with 2 second delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // TODO: Replace with actual API integration
      // const response = await callAPI.initiateCall({
      //   clientId: formData.clientId,
      //   clientName: formData.clientName
      // });

      setIsCallActive(true);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to initiate call:", error);
      // Handle error - maybe show a toast notification
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      // TODO: Replace with actual API integration
      // await callAPI.endCall();

      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to end call:", error);
    }
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleToggleHold = () => {
    setIsOnHold(!isOnHold);
  };

  // Timer effect for call duration
  useEffect(() => {
    let interval;
    if (isCallActive && !isOnHold) {
      interval = setInterval(() => {
        setCallTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive, isOnHold]);

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    if (!formData.clientId.trim()) {
      newErrors.clientId = "Client ID is required";
    }

    if (!formData.clientName.trim()) {
      newErrors.clientName = "Client name is required";
    }

    if (!formData.anchorName.trim()) {
      newErrors.anchorName = "Anchor name is required";
    }

    if (!formData.purpose.trim()) {
      newErrors.purpose = "Purpose is required";
    }

    if (!formData.date) {
      newErrors.date = "Date is required";
    }

    if (!formData.status.trim()) {
      newErrors.status = "Status is required";
    }

    if (!formData.businessSegment.trim()) {
      newErrors.businessSegment = "Business segment is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto h-[80vh] flex flex-col">
      <div className="flex-1 overflow-y-auto p-8">
        <form id="activity-form" onSubmit={handleSubmit} className="space-y-6">
          {/* Client ID and Client Name - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Client ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Client ID *
              </label>
              <Select
                name="clientId"
                value={clientIdOptions.find(
                  (option) => option.value === formData.clientId
                )}
                onChange={handleSelectChange}
                options={clientIdOptions}
                styles={selectStyles}
                placeholder="Select client ID"
                isSearchable
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {errors.clientId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.clientId}
                </p>
              )}
            </div>

            {/* Client Name - Auto-filled, Read-only */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Client Name *
              </label>
              <input
                type="text"
                name="clientName"
                value={formData.clientName}
                readOnly
                className="w-full px-3 py-3 border rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 outline-none border-gray-300 dark:border-gray-600 cursor-not-allowed"
                placeholder="Auto-filled based on Client ID"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This field will be auto-filled based on the selected Client ID
              </p>
            </div>
          </div>

          {/* Anchor Name - Auto-filled, Read-only */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Anchor Name *
            </label>
            <input
              type="text"
              name="anchorName"
              value={formData.anchorName}
              readOnly
              className="w-full px-3 py-3 border rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 outline-none border-gray-300 dark:border-gray-600 cursor-not-allowed"
              placeholder="Auto-filled based on Client ID"
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              This field will be auto-filled based on the selected Client ID
            </p>
          </div>

          {/* Type of Activity - Full Width Row */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Type of Activity *
            </label>
            <div className="flex space-x-6 mt-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="activityType"
                  value="call"
                  checked={formData.activityType === "call"}
                  onChange={(e) => handleActivityTypeChange(e.target.value)}
                  className="mr-2 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Call
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="activityType"
                  value="visit"
                  checked={formData.activityType === "visit"}
                  onChange={(e) => handleActivityTypeChange(e.target.value)}
                  className="mr-2 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Visit
                </span>
              </label>
            </div>
          </div>

          {/* Call Section - Only show when call is selected */}
          {formData.activityType === "call" && (
            <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
              {!isCallActive ? (
                // Call Button - Aligned to left
                <div className="flex justify-start">
                  <button
                    type="button"
                    onClick={handleStartCall}
                    disabled={isConnecting}
                    className="inline-flex items-center px-6 py-3 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm disabled:cursor-not-allowed"
                  >
                    <Phone size={20} className="mr-2" />
                    {isConnecting ? "Calling..." : "Call"}
                  </button>
                </div>
              ) : (
                // Call Controls
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  {/* Current User Display */}
                  <div className="mb-3 text-sm text-gray-600 dark:text-gray-300">
                    Currently talking to:{" "}
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {formData.clientName || "Client"}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    {/* Timer */}
                    <div className="text-lg font-mono text-gray-600 dark:text-gray-300">
                      {formatTimer(callTimer)}
                    </div>

                    {/* Call Control Buttons */}
                    <div className="flex items-center space-x-3">
                      {/* End Call Button */}
                      <button
                        type="button"
                        onClick={handleEndCall}
                        className="inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200"
                        style={{ backgroundColor: "#f46b68" }}
                        onMouseEnter={(e) =>
                          (e.target.style.backgroundColor = "#e55a57")
                        }
                        onMouseLeave={(e) =>
                          (e.target.style.backgroundColor = "#f46b68")
                        }
                      >
                        <PhoneMissed size={16} className="mr-1 text-white" />
                        <span className="text-white">End</span>
                      </button>

                      {/* Mute/Unmute Button */}
                      <button
                        type="button"
                        onClick={handleToggleMute}
                        className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                          isMuted ? "bg-transparent" : ""
                        }`}
                        style={!isMuted ? { backgroundColor: "#369dc9" } : {}}
                        onMouseEnter={(e) => {
                          if (!isMuted) {
                            e.target.style.backgroundColor = "#2a7ba7";
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!isMuted) {
                            e.target.style.backgroundColor = "#369dc9";
                          }
                        }}
                      >
                        {isMuted ? (
                          <>
                            <Mic size={16} className="mr-1 text-gray-500" />
                            <span className="text-gray-500">Unmute</span>
                          </>
                        ) : (
                          <>
                            <MicOff size={16} className="mr-1 text-white" />
                            <span className="text-white">Mute</span>
                          </>
                        )}
                      </button>

                      {/* Hold/Unhold Button */}
                      <button
                        type="button"
                        onClick={handleToggleHold}
                        className={`inline-flex items-center px-6 py-3 font-medium rounded-lg transition-colors duration-200 ${
                          isOnHold ? "bg-transparent" : ""
                        }`}
                        style={!isOnHold ? { backgroundColor: "#ffb800" } : {}}
                        onMouseEnter={(e) => {
                          if (!isOnHold) {
                            e.target.style.backgroundColor = "#e6a600";
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!isOnHold) {
                            e.target.style.backgroundColor = "#ffb800";
                          }
                        }}
                      >
                        {isOnHold ? (
                          <>
                            <Play size={16} className="mr-1 text-gray-500" />
                            <span className="text-gray-500">Unhold</span>
                          </>
                        ) : (
                          <>
                            <Pause size={16} className="mr-1 text-white" />
                            <span className="text-white">Hold</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Purpose and Date - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Purpose */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Purpose *
              </label>
              <Select
                name="purpose"
                value={purposeOptions.find(
                  (option) => option.value === formData.purpose
                )}
                onChange={handleSelectChange}
                options={purposeOptions}
                styles={selectStyles}
                placeholder="Select purpose"
                isSearchable
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {errors.purpose && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.purpose}
                </p>
              )}
            </div>

            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Date *
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className={`w-full px-3 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
                  errors.date
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
              />
              {errors.date && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.date}
                </p>
              )}
            </div>
          </div>

          {/* Comment on Outcome - Full Width */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Comment on Outcome
            </label>
            <textarea
              name="commentOnOutcome"
              value={formData.commentOnOutcome}
              onChange={handleChange}
              rows={4}
              className="w-full px-3 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
              placeholder="Enter comments about the outcome..."
            />
          </div>

          {/* Next Visit Schedule Date and Status - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Next Schedule Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Next Schedule Date
              </label>
              <input
                type="date"
                name="nextVisitScheduleDate"
                value={formData.nextVisitScheduleDate}
                onChange={handleChange}
                className="w-full px-3 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
              />
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status *
              </label>
              <Select
                name="status"
                value={statusOptions.find(
                  (option) => option.value === formData.status
                )}
                onChange={handleSelectChange}
                options={statusOptions}
                styles={selectStyles}
                placeholder="Select status"
                isSearchable
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {errors.status && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.status}
                </p>
              )}
            </div>
          </div>

          {/* Business Segment and Activity By - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Business Segment */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Business Segment *
              </label>
              <input
                type="text"
                name="businessSegment"
                value={formData.businessSegment}
                onChange={handleChange}
                className={`w-full px-3 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white outline-none transition-colors duration-200 ${
                  errors.businessSegment
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="Enter business segment"
              />
              {errors.businessSegment && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.businessSegment}
                </p>
              )}
            </div>

            {/* Activity By - Uneditable */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Activity By
              </label>
              <input
                type="text"
                name="activityBy"
                value={formData.activityBy}
                readOnly
                className="w-full px-3 py-3 border rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 outline-none border-gray-300 dark:border-gray-600 cursor-not-allowed"
                placeholder="Auto-filled"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This field will be auto-filled based on the logged-in user
              </p>
            </div>
          </div>
        </form>
      </div>

      {/* Form Actions - Fixed at bottom */}
      <div className="flex justify-end space-x-4 p-6 border-t border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800">
        <button
          type="button"
          onClick={onClose}
          className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          form="activity-form"
          disabled={isSubmitting}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting
            ? "Saving..."
            : item
            ? "Update Activity"
            : "Create Activity"}
        </button>
      </div>
    </div>
  );
};

export default ActivityForm;

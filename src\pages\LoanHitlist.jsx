import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import LeadForm from "../components/forms/LeadForm";
import CallForm from "../components/forms/CallForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import LeadProfile from "../components/forms/LeadProfile";
import { loanService, formatLoansForTable } from "../services/loanService";
import { downloadLoanCustomersTemplate } from "../utils/excelUtils";
import { toast } from "react-toastify";
import { Loader2 } from "lucide-react";

const LoanHitlist = () => {
  // Loading states
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // Data state
  const [loans, setLoans] = useState([]);

  // Modal states
  const [selectedItem, setSelectedItem] = useState(null);

  // Filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");



  // Fetch loans from API
  const fetchLoans = async () => {
    try {
      setLoading(true);
      console.log("Fetching loans from /loans endpoint...");
      const response = await loanService.getAll();
      console.log("Raw API response:", response);

      const formattedLoans = formatLoansForTable(response);
      console.log("Formatted loans for table:", formattedLoans);

      setLoans(formattedLoans);
    } catch (error) {
      console.error("Error fetching loans:", error);
      setLoans([]); // Set empty array on error
      toast.error("Failed to load loans data");
    } finally {
      setLoading(false);
    }
  };

  // Fetch loans on component mount
  useEffect(() => {
    fetchLoans();
  }, []);

  // Handle load more (for pagination)
  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate loading more data
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  // Handle create loan lead
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating new loan lead:", formData);
      const newLoanLead = await loanService.create(formData);

      // Add the new loan lead to the state
      const formattedLoanLead = formatLoansForTable({ data: [newLoanLead] })[0];
      setLoans(prevLoans => [formattedLoanLead, ...prevLoans]);

      toast.success("Loan lead created successfully!");
      return true;
    } catch (error) {
      console.error("Error creating loan lead:", error);
      toast.error(error.message || "Failed to create loan lead");
      return false;
    }
  };

  // Handle edit loan lead
  const handleEditSubmit = async (formData) => {
    try {
      console.log("Updating loan lead:", formData);
      const updatedLoanLead = await loanService.update(selectedItem.id, formData);

      // Update the loan lead in state
      const formattedLoanLead = formatLoansForTable({ data: [updatedLoanLead] })[0];
      setLoans(prevLoans =>
        prevLoans.map(loan =>
          loan.id === selectedItem.id ? formattedLoanLead : loan
        )
      );

      toast.success("Loan lead updated successfully!");
      return true;
    } catch (error) {
      console.error("Error updating loan lead:", error);
      toast.error(error.message || "Failed to update loan lead");
      return false;
    }
  };

  // Handle delete loan lead
  const handleDeleteConfirm = async (loanLead) => {
    try {
      console.log("Deleting loan lead:", loanLead);
      const success = await loanService.delete(loanLead.id);

      if (success) {
        // Remove the loan lead from state
        setLoans(prevLoans => prevLoans.filter(l => l.id !== loanLead.id));
        toast.success("Loan lead deleted successfully!");
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting loan lead:", error);
      toast.error(error.message || "Failed to delete loan lead");
    }
  };

  // Handle call action
  const handleCallSubmit = async (formData) => {
    try {
      console.log("Creating call for loan:", formData);
      const newCall = await loanService.calls.create({
        ...formData,
        loan_id: selectedItem.id,
      });
      
      toast.success("Call logged successfully!");
      return true;
    } catch (error) {
      console.error("Error creating call:", error);
      toast.error(error.message || "Failed to log call");
      return false;
    }
  };

  // Handle view loan
  const handleView = (loan) => {
    console.log("View loan:", loan);
    setSelectedItem(loan);
  };



  // Handle filter changes
  const handleFilterChange = (filterKey, value) => {
    console.log(`Filter changed: ${filterKey} = ${value}`);
    // Implement filter logic here
  };

  const handleClearFilters = () => {
    console.log("Clearing all filters");
    // Implement clear filters logic here
  };

  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  // Handle import functionality (same as leads)
  const handleImport = async (file) => {
    try {
      console.log("=== LOAN HITLIST IMPORT HANDLER ===");
      console.log("Raw file object:", file);
      console.log("File properties:");
      console.log("  - name:", file.name);
      console.log("  - size:", file.size);
      console.log("  - type:", file.type);
      console.log("  - lastModified:", file.lastModified);
      console.log("==============================");

      // Send only the file to backend
      const result = await loanService.importFromFile(file);
      console.log("Import result:", result);

      // Refresh the loan leads list
      await fetchLoans();

      // Show success message with count from backend response
      const count = result.inserted || result.count || "some";
      toast.success(`Successfully imported ${count} loan leads`);
    } catch (error) {
      console.error("Error importing loan leads:", error);

      // Handle different error types
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.message) {
        toast.error(error.message);
      } else {
        toast.error(
          "Failed to import loan leads. Please check the file format and try again."
        );
      }
    }
  };

  // Handle download template (loan customers template)
  const handleDownloadTemplate = () => {
    try {
      downloadLoanCustomersTemplate();
      toast.success("Loan customers template downloaded successfully!");
    } catch (error) {
      console.error("Error downloading loan customers template:", error);
      toast.error("Failed to download loan customers template");
    }
  };

  // Define table columns (similar to leads structure)
  const columns = [
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "phoneNumber",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "calls",
      title: "CALLS",
      render: (value) => (
        <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
          {value}
        </span>
      ),
    },
    {
      key: "lastInteraction",
      title: "LAST INTERACTION",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "officer",
      title: "OFFICER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Filter configuration (similar to leads)
  const filterConfig = [
    {
      key: "type",
      label: "Type",
      type: "select",
      options: [
        { value: "", label: "All Types" },
        { value: "new", label: "New" },
        { value: "existing", label: "Existing" },
      ],
    },
  ];

  return (
    <PrivateLayout pageTitle="Loan - Hit List">
      <div className="">
        <DataTable
          columns={columns}
          data={loans}
          searchPlaceholder="Search loan customers..."
          addButtonText="New Hit"
          onView={handleView}
          actions={[
            "call",
            "edit",
            "view",
          ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Loan Customers"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="type"
          highlightColors={{
            New: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
            Existing: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="loan customers"
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="created_at"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Modal forms
          createForm={({ onClose }) => (
            <LeadForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <LeadForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
              initialAnchorRelationship={
                item?.anchor_relationship_id && item?.anchor_relationship_name
                  ? {
                      id: item.anchor_relationship_id,
                      name: item.anchor_relationship_name,
                    }
                  : null
              }
              initialCustomerCategory={
                item?.customer_category
                  ? {
                      id: item.customer_category.id,
                      name: item.customer_category.name,
                    }
                  : null
              }
              initialIsicSector={
                item?.isic_sector
                  ? {
                      id: item.isic_sector.id,
                      name: item.isic_sector.name,
                    }
                  : null
              }
              initialBranch={
                item?.branch
                  ? {
                      id: item.branch.id,
                      name: item.branch.name,
                    }
                  : null
              }
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Loan Customer"
            />
          )}
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          profileForm={({ item, onClose }) => (
            <LeadProfile
              item={item}
              onClose={onClose}
            />
          )}
          createModalTitle="Create New Loan Customer"
          editModalTitle="Edit Loan Customer"
          deleteModalTitle=""
          callModalTitle="Make Call"
          profileModalTitle=""
          modalSize="xl"
          deleteModalSize="sm"
          callModalSize="lg"
          profileModalSize="xl"
          // Import/Export functionality
          showImportExport={true}
          onImport={handleImport}
          importModalTitle="Import Loan Customers"
          importTemplateFileName="Loan-Customers-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          onDownloadTemplate={handleDownloadTemplate}
        />
      </div>
    </PrivateLayout>
  );
};

export default LoanHitlist;

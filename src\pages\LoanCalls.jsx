import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import CallForm from "../components/forms/CallForm";
import { loanService, formatLoanCallsForTable } from "../services/loanService";
import { toast } from 'react-toastify';

const LoanCalls = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [calls, setCalls] = useState([]);
  const [error, setError] = useState(null);

  // Define table columns
  const columns = [
    {
      key: "customerName",
      title: "CUSTOMER NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "loanId",
      title: "LOAN ID",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "phoneNumber",
      title: "PHONE NUMBER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "callPurpose",
      title: "CALL PURPOSE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "callStatus",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === "Completed" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" :
          value === "Pending" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400" :
          value === "Failed" ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400" :
          "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
        }`}>
          {value}
        </span>
      ),
    },
    {
      key: "duration",
      title: "DURATION",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "officer",
      title: "OFFICER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "callDate",
      title: "CALL DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch calls data from API
  const fetchCalls = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching loan calls data...");

      const response = await loanService.calls.getAll();
      const formattedCalls = formatLoanCallsForTable(response);

      setCalls(formattedCalls);
      console.log("Loan calls data loaded successfully:", formattedCalls.length, "calls");
    } catch (error) {
      console.error("Error fetching loan calls:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load loan calls data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchCalls();
  }, []);

  // Handle load more
  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate loading more data
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  // Handle create call
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating new loan call:", formData);
      const newCall = await loanService.calls.create(formData);
      
      // Add the new call to the state
      const formattedCall = formatLoanCallsForTable({ data: [newCall] })[0];
      setCalls(prevCalls => [formattedCall, ...prevCalls]);
      
      toast.success("Loan call created successfully!");
      return true;
    } catch (error) {
      console.error("Error creating loan call:", error);
      toast.error(error.message || "Failed to create loan call");
      return false;
    }
  };

  // Handle edit call
  const handleEditSubmit = async (formData) => {
    try {
      console.log("Updating loan call:", formData);
      const updatedCall = await loanService.calls.update(formData.id, formData);
      
      // Update the call in state
      const formattedCall = formatLoanCallsForTable({ data: [updatedCall] })[0];
      setCalls(prevCalls => 
        prevCalls.map(call => 
          call.id === formData.id ? formattedCall : call
        )
      );
      
      toast.success("Loan call updated successfully!");
      return true;
    } catch (error) {
      console.error("Error updating loan call:", error);
      toast.error(error.message || "Failed to update loan call");
      return false;
    }
  };

  // Handle delete call
  const handleDeleteConfirm = async (call) => {
    try {
      console.log("Deleting loan call:", call);
      console.log(`Making DELETE request to /loan-calls/${call.id}`);

      const success = await loanService.calls.delete(call.id);

      if (success) {
        // Remove the call from state
        setCalls(prevCalls => prevCalls.filter(c => c.id !== call.id));
        toast.success("Loan call deleted successfully!");
        console.log("Loan call removed from table state");
      } else {
        throw new Error("Delete operation did not return success status");
      }
    } catch (error) {
      console.error("Error deleting loan call:", error);
      toast.error(error.message || "Failed to delete loan call");
    }
  };

  // Handle view call
  const handleView = (call) => {
    console.log("View loan call:", call);
    // Here you would typically navigate to view page or show view modal
  };

  // Handle call action (make another call)
  const handleCallSubmit = async (formData) => {
    try {
      console.log("Creating follow-up call:", formData);
      const newCall = await loanService.calls.create(formData);
      
      // Add the new call to the state
      const formattedCall = formatLoanCallsForTable({ data: [newCall] })[0];
      setCalls(prevCalls => [formattedCall, ...prevCalls]);
      
      toast.success("Follow-up call logged successfully!");
      return true;
    } catch (error) {
      console.error("Error creating follow-up call:", error);
      toast.error(error.message || "Failed to log follow-up call");
      return false;
    }
  };

  return (
    <PrivateLayout pageTitle="Loan Calls">
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={calls}
          searchPlaceholder="Search loan calls..."
          addButtonText="New Call"
          onView={handleView}
          actions={["call", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="callStatus"
          highlightColors={{
            Completed: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Failed: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="loan calls"
          // Modal forms
          createForm={({ onClose }) => (
            <CallForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Loan Call"
            />
          )}
          callForm={({ item, onClose }) => (
            <CallForm
              item={item}
              onClose={onClose}
              onSubmit={handleCallSubmit}
            />
          )}
          createModalTitle="Create New Loan Call"
          editModalTitle="Edit Loan Call"
          deleteModalTitle=""
          callModalTitle="Make Follow-up Call"
          modalSize="lg"
          deleteModalSize="sm"
          callModalSize="lg"
        />
      </div>
    </PrivateLayout>
  );
};

export default LoanCalls;

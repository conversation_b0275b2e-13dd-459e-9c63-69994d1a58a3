import { useState, useEffect, useMemo } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import CallForm from "../components/forms/CallForm";
import VisitForm from "../components/forms/VisitForm";
import { followUpsService, formatFollowUpsForTable } from "../services/followUpsService";
import { toast } from "react-toastify";
import { Phone, MapPin } from "lucide-react";

const FollowUps = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [followUps, setFollowUps] = useState([]);
  const [error, setError] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    status: "",
  });

  // Date range filter states
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");

  // Filter configuration for the DataTable
  const filterConfig = [
    {
      field: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "", label: "All Statuses" },
        { value: "Pending", label: "Pending" },
        { value: "Completed", label: "Completed" },
        { value: "Canceled", label: "Cancelled" }, // Note: data uses "Canceled" but display "Cancelled"
      ],
    },
  ];

  // Filter change handlers
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      status: "",
    });
  };

  // Date change handlers for DataTable
  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "clientName",
      title: "CLIENT NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchorName",
      title: "ANCHOR NAME",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value || "N/A"}
        </span>
      ),
    },
    {
      key: "assignedOfficer",
      title: "ASSIGNED OFFICER",
      render: (value, item) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <div className="font-medium">{value}</div>
          {item.assignedOfficerCode && item.assignedOfficerCode !== "N/A" && (
            <div className="text-xs text-gray-500 dark:text-gray-500">
              {item.assignedOfficerCode}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "followUpType",
      title: "FOLLOW-UP TYPE",
      render: (value) => {
        const getTypeColor = (type) => {
          switch (type) {
            case "Phone Call":
              return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
            case "Site Visit":
              return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
            default:
              return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
          }
        };

        const getTypeIcon = (type) => {
          switch (type) {
            case "Phone Call":
              return <Phone size={12} className="mr-1" />;
            case "Site Visit":
              return <MapPin size={12} className="mr-1" />;
            default:
              return null;
          }
        };

        return (
          <span
            className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getTypeColor(
              value
            )}`}
          >
            {getTypeIcon(value)}
            {value}
          </span>
        );
      },
    },
    {
      key: "followUpReason",
      title: "FOLLOW-UP REASON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => {
        const getStatusColor = (status) => {
          switch (status) {
            case "Pending":
              return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
            case "Completed":
              return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
            case "Canceled":
              return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
            default:
              return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
          }
        };

        return (
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
              value
            )}`}
          >
            {value}
          </span>
        );
      },
    },
    {
      key: "createdDate",
      title: "CREATED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
  ];

  // Fetch follow-ups from API
  const fetchFollowUps = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching follow-ups from API...");

      const response = await followUpsService.getAll();
      const formattedFollowUps = formatFollowUpsForTable(response);

      setFollowUps(formattedFollowUps);
      console.log("Follow-ups data loaded successfully:", formattedFollowUps.length, "follow-ups");
    } catch (error) {
      console.error("Error fetching follow-ups:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load follow-ups data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchFollowUps();
  }, []);

  // Handle status update
  const handleStatusUpdate = async (followUpId, newStatus) => {
    try {
      console.log(`Updating follow-up ${followUpId} status to ${newStatus}`);

      const response = await followUpsService.updateStatus(followUpId, newStatus);

      // Update the follow-up in state
      setFollowUps(prevFollowUps =>
        prevFollowUps.map(followUp =>
          followUp.id === followUpId
            ? { ...followUp, status: newStatus.charAt(0).toUpperCase() + newStatus.slice(1).toLowerCase() }
            : followUp
        )
      );

      // Refresh data to get updated information
      await fetchFollowUps();

      toast.success(`Follow-up status updated to ${newStatus}`);
      return true;
    } catch (error) {
      console.error("Error updating follow-up status:", error);
      toast.error(error.message || "Failed to update follow-up status");
      return false;
    }
  };

  // Handle call activity creation from follow-up
  const handleCallSubmit = async (callData, leadItem, error) => {
    console.log("Call submitted from follow-up:", { callData, leadItem, error });

    // If call was created successfully (no error), handle follow-up completion
    if (!error && callData && leadItem) {
      console.log("Call created successfully from follow-up");

      // Ask user if they want to mark follow-up as completed
      const shouldComplete = window.confirm(
        "Call activity created successfully! Would you like to mark this follow-up as completed?"
      );

      if (shouldComplete) {
        try {
          await handleStatusUpdate(selectedItem.id, "completed");
          toast.success("Call activity created and follow-up marked as completed!");
        } catch (statusError) {
          console.error("Error updating follow-up status:", statusError);
          toast.success("Call activity created successfully!");
          toast.error("Failed to update follow-up status");
        }
      } else {
        toast.success("Call activity created successfully!");
      }

      // Refresh follow-ups data to get updated information
      await fetchFollowUps();
    } else if (error) {
      console.error("Error creating call activity:", error);
      toast.error("Failed to create call activity");
    }
  };

  // Handle visit activity creation from follow-up
  const handleVisitSubmit = async (visitData, leadItem, error) => {
    console.log("Visit submitted from follow-up:", { visitData, leadItem, error });

    // If visit was created successfully (no error), handle follow-up completion
    if (!error && visitData && leadItem) {
      console.log("Visit created successfully from follow-up");

      // Ask user if they want to mark follow-up as completed
      const shouldComplete = window.confirm(
        "Visit activity created successfully! Would you like to mark this follow-up as completed?"
      );

      if (shouldComplete) {
        try {
          await handleStatusUpdate(selectedItem.id, "completed");
          toast.success("Visit activity created and follow-up marked as completed!");
        } catch (statusError) {
          console.error("Error updating follow-up status:", statusError);
          toast.success("Visit activity created successfully!");
          toast.error("Failed to update follow-up status");
        }
      } else {
        toast.success("Visit activity created successfully!");
      }

      // Refresh follow-ups data to get updated information
      await fetchFollowUps();
    } else if (error) {
      console.error("Error creating visit activity:", error);
      toast.error("Failed to create visit activity");
    }
  };

  // Handle custom actions (status updates and activities)
  const handleCustomAction = async (action, followUp) => {
    console.log(`=== CUSTOM ACTION: ${action} ===`);
    console.log('Follow-up data:', followUp);
    console.log('followUp.leadId:', followUp.leadId);
    console.log('followUp.clientName:', followUp.clientName);
    console.log('================================');

    setSelectedItem(followUp);

    switch (action) {
      case "set-completed":
        return await handleStatusUpdate(followUp.id, "completed");
      case "set-cancelled":
        return await handleStatusUpdate(followUp.id, "cancelled");
      case "call":
      case "visit":
        // These will be handled by the modal forms
        break;
      default:
        console.log(`Unhandled custom action: ${action}`);
    }
  };



  const handleEditSubmit = (formData) => {
    console.log("Updating follow-up:", formData);
    // API Integration: PUT /api/follow-ups/{id}
    // const response = await fetch(`/api/follow-ups/${formData.id}`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(formData)
    // });
  };







  const handleView = async (followUp) => {
    setSelectedItem(followUp); // Set selected item for view
    console.log("View follow-up:", followUp);

    try {
      // API Integration: GET /api/follow-ups/{id}
      // Uncomment when API is ready
      // const response = await fetch(`/api/follow-ups/${followUp.id}`);

      // if (!response.ok) {
      //   throw new Error('Failed to fetch follow-up details');
      // }

      // const detailedFollowUp = await response.json();
      // console.log("Detailed follow-up data:", detailedFollowUp);

      // For now, simulate API call
      console.log("Simulated API call for viewing follow-up:", followUp.id);

      // Here you would typically navigate to view page or show view modal
      // For example: navigate(`/follow-ups/${followUp.id}`);
    } catch (error) {
      console.error("Error fetching follow-up details:", error);
      // Here you would typically show an error notification
    }
  };



  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more follow-ups");
    }, 2000);
  };

  return (
    <PrivateLayout pageTitle="Follow-ups">
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={followUps}
          searchPlaceholder="Search follow-ups..."
          onView={handleView}
          actions={[
            "call",
            "visit",
            "set-completed",
            "set-cancelled"
          ]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Follow-ups"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          onStatusUpdate={handleStatusUpdate}
          onCustomAction={handleCustomAction}
          highlightColors={{
            Pending:
              "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Completed:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Canceled:
              "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="follow-ups"
          // Filters
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="date"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
          // Modal forms
          callForm={({ item, onClose }) => {
            console.log("=== CALL FORM RENDER ===");
            console.log("DataTable selectedItem:", item);
            console.log("Follow-ups selectedItem:", selectedItem);
            console.log("========================");

            // Memoize the call form item to prevent unnecessary re-renders
            const callFormItem = useMemo(() => {
              if (!item) return null;

              return {
                ...item,
                id: item.leadId, // CallForm expects item.id to be the leadID
                name: item.clientName, // Add name for display
              };
            }, [item?.id, item?.leadId, item?.clientName]);

            console.log("callFormItem created:", callFormItem);
            console.log("callFormItem.id:", callFormItem?.id);

            return (
              <CallForm
                item={callFormItem}
                onClose={onClose}
                onSubmit={handleCallSubmit}
              />
            );
          }}
          visitForm={({ item, onClose }) => {
            // Memoize the visit form item to prevent unnecessary re-renders
            const visitFormItem = useMemo(() => {
              if (!item) return null;

              return {
                ...item,
                id: item.leadId, // VisitForm expects item.id to be the leadID
                name: item.clientName, // Add name for display
              };
            }, [item?.id, item?.leadId, item?.clientName]);

            return (
              <VisitForm
                item={visitFormItem}
                onClose={onClose}
                onSubmit={handleVisitSubmit}
              />
            );
          }}
          callModalTitle={
            selectedItem
              ? `Call ${selectedItem.clientName}`
              : "Make Call"
          }
          visitModalTitle={
            selectedItem
              ? `Visit ${selectedItem.clientName}`
              : "Schedule Visit"
          }
          modalSize="lg"
          callModalSize="lg"
          visitModalSize="lg"
        />
      </div>
    </PrivateLayout>
  );
};

export default FollowUps;

import instance from '../axios/instance.jsx';

// Loan API endpoints (using loan-clients endpoint)
const ENDPOINTS = {
  LOANS: '/loan-clients',
  LOANS_CREATE: '/loan-clients',
  LOAN_BY_ID: (id) => `/loan-clients/${id}`,
  LOAN_DETAILS: (id) => `/loan-clients/${id}`,
  LOAN_ATTACHMENTS: (id) => `/loan-clients/${id}/attachments`,
  LOAN_IMPORT: '/loan-clients/import',
  LOAN_CALLS: '/loan-calls',
  LOAN_CALL_BY_ID: (id) => `/loan-calls/${id}`,
  LOAN_FOLLOW_UPS: '/loan-follow-ups',
  LOAN_FOLLOW_UP_BY_ID: (id) => `/loan-follow-ups/${id}`,
};

// Loan Service (using loan-clients endpoint)
export const loanService = {
  // Get all loan clients for hitlist
  getAll: async () => {
    try {
      console.log('=== FETCHING ALL LOAN CLIENTS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.LOANS}`);

      const response = await instance.get(ENDPOINTS.LOANS);

      console.log('Loan clients response:', response.data);
      console.log('==============================');

      return response.data;
    } catch (error) {
      console.error('Error fetching loan clients:', error);
      throw error;
    }
  },

  // Create a new loan client
  create: async (loanData) => {
    try {
      console.log('=== CREATING NEW LOAN CLIENT ===');
      console.log('Loan client data:', loanData);
      console.log(`API Endpoint: POST ${ENDPOINTS.LOANS_CREATE}`);

      const response = await instance.post(ENDPOINTS.LOANS_CREATE, loanData);

      console.log('Loan client creation response:', response.data);
      console.log('=============================');
      
      return response.data;
    } catch (error) {
      console.error('Error creating loan client:', error);
      throw error;
    }
  },

  // Update an existing loan client
  update: async (id, loanData) => {
    try {
      console.log('=== UPDATING LOAN CLIENT ===');
      console.log(`Loan client ID: ${id}`);
      console.log('Loan client data:', loanData);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.LOAN_BY_ID(id)}`);

      const response = await instance.patch(ENDPOINTS.LOAN_BY_ID(id), loanData);

      console.log('Loan client update response:', response.data);
      console.log('========================');

      return response.data;
    } catch (error) {
      console.error('Error updating loan client:', error);
      throw error;
    }
  },

  // Delete a loan client
  delete: async (id) => {
    try {
      console.log('=== DELETING LOAN CLIENT ===');
      console.log(`Loan client ID: ${id}`);
      console.log(`API Endpoint: DELETE ${ENDPOINTS.LOAN_BY_ID(id)}`);

      const response = await instance.delete(ENDPOINTS.LOAN_BY_ID(id));

      console.log('Loan client deletion response:', response.status);
      console.log('=====================');

      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting loan client:', error);
      throw error;
    }
  },

  // Get a single loan client by ID
  getById: async (id) => {
    try {
      console.log('=== FETCHING LOAN CLIENT BY ID ===');
      console.log(`Loan client ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.LOAN_BY_ID(id)}`);
      
      const response = await instance.get(ENDPOINTS.LOAN_BY_ID(id));
      
      console.log('Loan by ID response:', response.data);
      console.log('===========================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching loan by ID:', error);
      throw error;
    }
  },

  // Get detailed loan information for editing
  getDetails: async (id) => {
    try {
      console.log('=== FETCHING LOAN DETAILS ===');
      console.log(`Loan ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.LOAN_DETAILS(id)}`);
      
      const response = await instance.get(ENDPOINTS.LOAN_DETAILS(id));
      
      console.log('Loan details response:', response.data);
      console.log('=============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching loan details:', error);
      throw error;
    }
  },

  // Get loan attachments
  getAttachments: async (id) => {
    try {
      console.log('=== FETCHING LOAN ATTACHMENTS ===');
      console.log(`Loan ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.LOAN_ATTACHMENTS(id)}`);

      const response = await instance.get(ENDPOINTS.LOAN_ATTACHMENTS(id));

      console.log('Loan attachments response:', response.data);
      console.log('=================================');

      return response.data;
    } catch (error) {
      console.error('Error fetching loan attachments:', error);
      throw error;
    }
  },

  // Import loan leads from file (same as leads import)
  importFromFile: async (file) => {
    try {
      console.log('=== IMPORTING LOAN LEADS FROM FILE ===');
      console.log('File:', file);
      console.log(`API Endpoint: POST ${ENDPOINTS.LOAN_IMPORT}`);

      const formData = new FormData();
      formData.append('file', file);

      const response = await instance.post(ENDPOINTS.LOAN_IMPORT, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Loan leads import response:', response.data);
      console.log('=====================================');

      return response.data;
    } catch (error) {
      console.error('Error importing loan leads:', error);
      throw error;
    }
  },

  // Loan Calls API methods
  calls: {
    // Get all loan calls
    getAll: async () => {
      try {
        console.log('=== FETCHING ALL LOAN CALLS ===');
        console.log(`API Endpoint: GET ${ENDPOINTS.LOAN_CALLS}`);
        
        const response = await instance.get(ENDPOINTS.LOAN_CALLS);
        
        console.log('Loan calls response:', response.data);
        console.log('===============================');
        
        return response.data;
      } catch (error) {
        console.error('Error fetching loan calls:', error);
        throw error;
      }
    },

    // Create a new loan call
    create: async (callData) => {
      try {
        console.log('=== CREATING NEW LOAN CALL ===');
        console.log('Loan call data:', callData);
        console.log(`API Endpoint: POST ${ENDPOINTS.LOAN_CALLS}`);
        
        const response = await instance.post(ENDPOINTS.LOAN_CALLS, callData);
        
        console.log('Loan call creation response:', response.data);
        console.log('==============================');
        
        return response.data;
      } catch (error) {
        console.error('Error creating loan call:', error);
        throw error;
      }
    },

    // Update an existing loan call
    update: async (id, callData) => {
      try {
        console.log('=== UPDATING LOAN CALL ===');
        console.log(`Loan call ID: ${id}`);
        console.log('Loan call data:', callData);
        console.log(`API Endpoint: PATCH ${ENDPOINTS.LOAN_CALL_BY_ID(id)}`);
        
        const response = await instance.patch(ENDPOINTS.LOAN_CALL_BY_ID(id), callData);
        
        console.log('Loan call update response:', response.data);
        console.log('==========================');
        
        return response.data;
      } catch (error) {
        console.error('Error updating loan call:', error);
        throw error;
      }
    },

    // Delete a loan call
    delete: async (id) => {
      try {
        console.log('=== DELETING LOAN CALL ===');
        console.log(`Loan call ID: ${id}`);
        console.log(`API Endpoint: DELETE ${ENDPOINTS.LOAN_CALL_BY_ID(id)}`);
        
        const response = await instance.delete(ENDPOINTS.LOAN_CALL_BY_ID(id));
        
        console.log('Loan call deletion response:', response.status);
        console.log('==========================');
        
        return response.status === 204 || response.status === 200;
      } catch (error) {
        console.error('Error deleting loan call:', error);
        throw error;
      }
    },
  },

  // Loan Follow-ups API methods
  followUps: {
    // Get all loan follow-ups
    getAll: async () => {
      try {
        console.log('=== FETCHING ALL LOAN FOLLOW-UPS ===');
        console.log(`API Endpoint: GET ${ENDPOINTS.LOAN_FOLLOW_UPS}`);
        
        const response = await instance.get(ENDPOINTS.LOAN_FOLLOW_UPS);
        
        console.log('Loan follow-ups response:', response.data);
        console.log('====================================');
        
        return response.data;
      } catch (error) {
        console.error('Error fetching loan follow-ups:', error);
        throw error;
      }
    },

    // Create a new loan follow-up
    create: async (followUpData) => {
      try {
        console.log('=== CREATING NEW LOAN FOLLOW-UP ===');
        console.log('Loan follow-up data:', followUpData);
        console.log(`API Endpoint: POST ${ENDPOINTS.LOAN_FOLLOW_UPS}`);
        
        const response = await instance.post(ENDPOINTS.LOAN_FOLLOW_UPS, followUpData);
        
        console.log('Loan follow-up creation response:', response.data);
        console.log('==================================');
        
        return response.data;
      } catch (error) {
        console.error('Error creating loan follow-up:', error);
        throw error;
      }
    },

    // Update an existing loan follow-up
    update: async (id, followUpData) => {
      try {
        console.log('=== UPDATING LOAN FOLLOW-UP ===');
        console.log(`Loan follow-up ID: ${id}`);
        console.log('Loan follow-up data:', followUpData);
        console.log(`API Endpoint: PATCH ${ENDPOINTS.LOAN_FOLLOW_UP_BY_ID(id)}`);
        
        const response = await instance.patch(ENDPOINTS.LOAN_FOLLOW_UP_BY_ID(id), followUpData);
        
        console.log('Loan follow-up update response:', response.data);
        console.log('===============================');
        
        return response.data;
      } catch (error) {
        console.error('Error updating loan follow-up:', error);
        throw error;
      }
    },

    // Delete a loan follow-up
    delete: async (id) => {
      try {
        console.log('=== DELETING LOAN FOLLOW-UP ===');
        console.log(`Loan follow-up ID: ${id}`);
        console.log(`API Endpoint: DELETE ${ENDPOINTS.LOAN_FOLLOW_UP_BY_ID(id)}`);
        
        const response = await instance.delete(ENDPOINTS.LOAN_FOLLOW_UP_BY_ID(id));
        
        console.log('Loan follow-up deletion response:', response.status);
        console.log('=================================');
        
        return response.status === 204 || response.status === 200;
      } catch (error) {
        console.error('Error deleting loan follow-up:', error);
        throw error;
      }
    },
  },
};

// Helper function to format date and time
const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

// Helper function to format loan status
const formatStatus = (status) => {
  if (!status) return 'Unknown';
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
};

// Helper function to format loan amount
const formatAmount = (amount) => {
  if (!amount || amount === 0) return 'N/A';
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Data formatter function for loan leads table (similar to leads structure)
export const formatLoansForTable = (apiResponse) => {
  console.log('=== FORMATTING LOAN LEADS FOR TABLE ===');
  console.log('Raw API response:', apiResponse);

  if (!apiResponse || !Array.isArray(apiResponse.data)) {
    console.warn('Invalid API response format for loan leads');
    return [];
  }

  // Helper function to format anchor display name
  const formatAnchorName = (loanLead) => {
    if (loanLead.anchor?.name) {
      return loanLead.anchor.name;
    }
    if (loanLead.parent_lead_name) {
      return loanLead.parent_lead_name;
    }
    return null;
  };

  const formattedLoanLeads = apiResponse.data.map((loanLead, index) => ({
    id: loanLead.id || `loan-lead${index + 1}`,
    anchor: formatAnchorName(loanLead) || "N/A",
    name: loanLead.lead_name || loanLead.customer_name || "Unknown Customer",
    phoneNumber: loanLead.phoneNumber || loanLead.phone_number || "N/A",
    account_number: loanLead.account_number,
    visits: loanLead.no_of_visits || 0,
    calls: loanLead.no_of_calls || 0,
    lastInteraction: loanLead.last_interaction || "N/A",
    lastInteractionType: loanLead.last_interaction_type || null,
    status: formatStatus(loanLead.status),
    // Date fields for filtering
    created_at: loanLead.created_at || loanLead.createdDate || loanLead.created_date,
    // Anchor relationship fields for edit form
    anchor_relationship_id: loanLead.anchor_relationship_id,
    anchor_relationship_name: loanLead.anchor_relationship_name,
    parent_lead_id: loanLead.parent_lead_id,
    // Store full anchor object for reference
    anchorObject: loanLead.anchor,
    // Employer name field for edit form
    employerName: loanLead.employerName,
    officer: loanLead.officer || loanLead.loan_officer || "N/A",
    // Additional fields for form compatibility
    category: "N/A",
    branchName: "N/A",
    type: "New", // Default assumption
    isicSector: "N/A",
    clientId: "",
    contactPersonName: "",
    contactPersonPhone: "",
    // Profile specific fields
    email: "N/A",
    age: "N/A",
    gender: "N/A",
    region: "N/A",
    hitListGroup: "N/A",
    // Store original data for editing
    originalData: loanLead,
  }));

  console.log('Formatted loan leads for table:', formattedLoanLeads);
  console.log('=======================================');

  return formattedLoanLeads;
};

// Data formatter function for loan calls table
export const formatLoanCallsForTable = (apiResponse) => {
  console.log('=== FORMATTING LOAN CALLS FOR TABLE ===');
  console.log('Raw API response:', apiResponse);

  if (!apiResponse || !Array.isArray(apiResponse.data)) {
    console.warn('Invalid API response format for loan calls');
    return [];
  }

  const formattedCalls = apiResponse.data.map((call, index) => ({
    id: call.id || `loan-call${index + 1}`,
    customerName: call.customer_name || call.borrower_name || "Unknown Customer",
    loanId: call.loan_id || "N/A",
    phoneNumber: call.phone_number || call.customer_phone || "N/A",
    callPurpose: call.call_purpose || call.purpose || "N/A",
    callStatus: formatStatus(call.call_status || call.status),
    callDate: call.call_date ? formatDateTime(call.call_date) : "N/A",
    duration: call.duration_minutes ? `${call.duration_minutes} min` : "N/A",
    notes: call.notes || call.description || "N/A",
    officer: call.officer || call.made_by || "N/A",
    nextFollowUp: call.next_follow_up ? formatDateTime(call.next_follow_up) : "N/A",
    created_at: call.created_at || call.createdDate || call.created_date,
    // Store original data for editing
    originalData: call,
  }));

  console.log('Formatted loan calls for table:', formattedCalls);
  console.log('=======================================');

  return formattedCalls;
};

// Data formatter function for loan follow-ups table
export const formatLoanFollowUpsForTable = (apiResponse) => {
  console.log('=== FORMATTING LOAN FOLLOW-UPS FOR TABLE ===');
  console.log('Raw API response:', apiResponse);

  if (!apiResponse || !Array.isArray(apiResponse.data)) {
    console.warn('Invalid API response format for loan follow-ups');
    return [];
  }

  const formattedFollowUps = apiResponse.data.map((followUp, index) => ({
    id: followUp.id || `loan-followup${index + 1}`,
    customerName: followUp.customer_name || followUp.borrower_name || "Unknown Customer",
    loanId: followUp.loan_id || "N/A",
    followUpType: followUp.follow_up_type || followUp.type || "N/A",
    followUpReason: followUp.follow_up_reason || followUp.reason || "N/A",
    scheduledDate: followUp.scheduled_date ? formatDateTime(followUp.scheduled_date) : "N/A",
    status: formatStatus(followUp.status),
    assignedOfficer: followUp.assigned_officer || followUp.officer || "N/A",
    notes: followUp.notes || followUp.description || "N/A",
    createdDate: followUp.created_at ? formatDateTime(followUp.created_at) : "N/A",
    created_at: followUp.created_at || followUp.createdDate || followUp.created_date,
    // Store original data for editing
    originalData: followUp,
  }));

  console.log('Formatted loan follow-ups for table:', formattedFollowUps);
  console.log('============================================');

  return formattedFollowUps;
};

export default loanService;

# Comprehensive Pagination System Documentation

## Overview

This document describes the complete pagination system implementation for the DataTable component, including backend API integration, frontend components, and usage examples.

## System Architecture

### 🏗️ **Components Overview**

1. **`Pagination.jsx`** - Reusable pagination UI component
2. **`usePagination.js`** - Custom hook for pagination logic
3. **`DataTable.jsx`** - Enhanced with pagination support
4. **Backend API Integration** - Standardized pagination parameters

### 🔄 **Data Flow**

```
Page Component → usePagination Hook → API Call → Backend → Response → UI Update
```

## Backend API Integration

### **Request Parameters**
```javascript
interface PaginationParams {
  page?: number;        // Page number (1-based), default: 1
  limit?: number;       // Items per page, default: 10, max: 100
  search?: string;      // Optional search term
}
```

### **Response Format**
```javascript
interface PaginatedResponse<T> {
  data: T[];           // Array of items for current page
  meta: {
    total: number;           // Total number of items
    page: number;            // Current page number
    limit: number;           // Items per page
    totalPages: number;      // Total number of pages
    hasNextPage: boolean;    // Whether there's a next page
    hasPreviousPage: boolean; // Whether there's a previous page
  };
}
```

### **Example API Call**
```javascript
GET /api/v1/branches?page=2&limit=20&search=central

Response:
{
  "data": [
    {
      "id": "branch-uuid",
      "name": "Central Branch",
      "region": { "id": "region-uuid", "name": "Central Region" },
      "created_at": "2025-08-01T10:30:00.000Z"
    }
  ],
  "meta": {
    "total": 156,
    "page": 2,
    "limit": 20,
    "totalPages": 8,
    "hasNextPage": true,
    "hasPreviousPage": true
  }
}
```

## Frontend Implementation

### **1. usePagination Hook**

#### **Basic Usage:**
```javascript
import { usePagination } from '../hooks/usePagination';

const {
  data,
  loading,
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  goToPage,
  changePageSize,
  refresh,
} = usePagination({
  fetchData: async (params) => {
    return await apiService.getAll(params);
  },
  initialPageSize: 10,
  autoFetch: true,
});
```

#### **Advanced Usage with Dependencies:**
```javascript
const [filters, setFilters] = useState({ status: '', role: '' });

const pagination = usePagination({
  fetchData: useCallback(async (params) => {
    return await apiService.getAll({
      ...params,
      ...filters, // Include filters in API call
    });
  }, [filters]),
  dependencies: [filters], // Refetch when filters change
  initialPageSize: 20,
});
```

### **2. Pagination Component**

#### **Props:**
```javascript
<Pagination
  currentPage={1}
  totalPages={10}
  totalItems={200}
  itemsPerPage={20}
  onPageChange={(page) => goToPage(page)}
  onPageSizeChange={(size) => changePageSize(size)}
  showPageSizeSelector={true}
  pageSizeOptions={[10, 20, 50, 100]}
  showItemsInfo={true}
  showFirstLast={true}
  maxVisiblePages={5}
  loading={false}
/>
```

#### **Features:**
- **Page Navigation**: First, Previous, Next, Last buttons
- **Page Numbers**: Clickable page numbers with ellipsis
- **Page Size Selector**: Dropdown to change items per page
- **Items Info**: "Showing X-Y of Z items"
- **Loading States**: Disabled during API calls
- **Responsive Design**: Mobile-friendly layout

### **3. Enhanced DataTable**

#### **Pagination Props:**
```javascript
<DataTable
  // ... existing props
  
  // Pagination props
  showPagination={true}
  currentPage={currentPage}
  totalPages={totalPages}
  totalItems={totalItems}
  pageSize={pageSize}
  onPageChange={handlePageChange}
  onPageSizeChange={handlePageSizeChange}
  pageSizeOptions={[10, 20, 50, 100]}
  showPageSizeSelector={true}
  showItemsInfo={true}
  showFirstLast={true}
  maxVisiblePages={5}
  
  // Legacy support (backward compatibility)
  loadingMore={false}
  onLoadMore={handleLoadMore}
  showLoadMore={false}
/>
```

## Implementation Examples

### **Example 1: Basic Pagination**

```javascript
// pages/Branches.jsx
import { usePagination } from '../hooks/usePagination';
import { useApi } from '../contexts/ApiContext';

const Branches = () => {
  const { branchApi } = useApi();
  
  const {
    data: branches,
    loading,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    goToPage,
    changePageSize,
    refresh,
  } = usePagination({
    fetchData: useCallback(async (params) => {
      return await branchApi.getAll(params);
    }, [branchApi]),
    initialPageSize: 10,
  });

  return (
    <DataTable
      columns={columns}
      data={branches}
      loading={loading}
      showPagination={true}
      currentPage={currentPage}
      totalPages={totalPages}
      totalItems={totalItems}
      pageSize={pageSize}
      onPageChange={goToPage}
      onPageSizeChange={changePageSize}
    />
  );
};
```

### **Example 2: Pagination with Search and Filters**

```javascript
const Users = () => {
  const { userApi } = useApi();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({ role: '', status: '' });

  const pagination = usePagination({
    fetchData: useCallback(async (params) => {
      return await userApi.getAll({
        ...params,
        search: searchTerm,
        ...filters,
      });
    }, [userApi, searchTerm, filters]),
    dependencies: [searchTerm, filters],
    initialPageSize: 20,
  });

  const handleSearch = (term) => {
    setSearchTerm(term);
    // usePagination will automatically refetch due to dependencies
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // usePagination will automatically refetch due to dependencies
  };

  return (
    <DataTable
      columns={columns}
      data={pagination.data}
      loading={pagination.loading}
      showPagination={true}
      currentPage={pagination.currentPage}
      totalPages={pagination.totalPages}
      totalItems={pagination.totalItems}
      pageSize={pagination.pageSize}
      onPageChange={pagination.goToPage}
      onPageSizeChange={pagination.changePageSize}
      // Search integration
      searchValue={searchTerm}
      onSearchChange={handleSearch}
      // Filter integration
      filters={filters}
      onFilterChange={handleFilterChange}
    />
  );
};
```

### **Example 3: Load More Pagination (Legacy Support)**

```javascript
import { useLoadMorePagination } from '../hooks/usePagination';

const Products = () => {
  const { productApi } = useApi();
  
  const {
    data: products,
    loading,
    loadingMore,
    hasMore,
    loadMore,
  } = useLoadMorePagination({
    fetchData: async (params) => {
      return await productApi.getAll(params);
    },
    initialPageSize: 10,
  });

  return (
    <DataTable
      columns={columns}
      data={products}
      loading={loading}
      loadingMore={loadingMore}
      onLoadMore={loadMore}
      showLoadMore={hasMore}
      loadMoreText="Load More Products"
    />
  );
};
```

## Migration Guide

### **From Load More to Pagination**

#### **Before (Load More):**
```javascript
const [data, setData] = useState([]);
const [loading, setLoading] = useState(false);
const [loadingMore, setLoadingMore] = useState(false);

const fetchData = async () => {
  setLoading(true);
  const response = await api.getAll();
  setData(response.data);
  setLoading(false);
};

const loadMore = async () => {
  setLoadingMore(true);
  const response = await api.getAll({ page: page + 1 });
  setData(prev => [...prev, ...response.data]);
  setLoadingMore(false);
};

<DataTable
  data={data}
  loading={loading}
  loadingMore={loadingMore}
  onLoadMore={loadMore}
  showLoadMore={true}
/>
```

#### **After (Pagination):**
```javascript
const pagination = usePagination({
  fetchData: async (params) => await api.getAll(params),
  initialPageSize: 10,
});

<DataTable
  data={pagination.data}
  loading={pagination.loading}
  showPagination={true}
  currentPage={pagination.currentPage}
  totalPages={pagination.totalPages}
  totalItems={pagination.totalItems}
  pageSize={pagination.pageSize}
  onPageChange={pagination.goToPage}
  onPageSizeChange={pagination.changePageSize}
/>
```

## Performance Considerations

### **Optimizations:**
1. **Memoized Callbacks**: Use `useCallback` for fetchData functions
2. **Dependency Management**: Only include necessary dependencies
3. **Debounced Search**: Implement search debouncing for better UX
4. **Caching**: Consider implementing client-side caching for frequently accessed pages

### **Best Practices:**
1. **Page Size Limits**: Respect backend limits (max 100 items per page)
2. **Loading States**: Always show loading indicators during API calls
3. **Error Handling**: Implement comprehensive error handling
4. **Accessibility**: Ensure pagination controls are keyboard accessible

## Troubleshooting

### **Common Issues:**

#### **1. Data Not Loading**
- Check if `fetchData` function is properly defined
- Verify API endpoint returns correct response format
- Check browser console for API errors

#### **2. Pagination Not Updating**
- Ensure `onPageChange` and `onPageSizeChange` are properly connected
- Verify `currentPage`, `totalPages`, etc. props are passed correctly
- Check if backend API supports pagination parameters

#### **3. Search/Filter Integration**
- Include search/filter parameters in `fetchData` function
- Add search/filter state to `dependencies` array
- Ensure backend API handles search/filter parameters

### **Debug Tips:**
1. **Console Logging**: Enable detailed logging in `usePagination` hook
2. **Network Tab**: Check API requests in browser dev tools
3. **State Inspection**: Use React DevTools to inspect pagination state

## Future Enhancements

### **Planned Features:**
1. **Virtual Scrolling**: For very large datasets
2. **Infinite Scroll**: Alternative to traditional pagination
3. **Sorting Integration**: Column sorting with pagination
4. **Advanced Filtering**: Complex filter combinations
5. **Export Integration**: Export all pages or current page
6. **Bookmark Support**: URL-based pagination state

### **API Enhancements:**
1. **Cursor-based Pagination**: For real-time data
2. **Bulk Operations**: Multi-page selections
3. **Caching Headers**: Better cache management
4. **GraphQL Support**: Alternative to REST pagination

The pagination system is now **production-ready** and provides a comprehensive solution for handling large datasets with excellent user experience! 🚀

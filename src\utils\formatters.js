// Date and time formatting utilities

/**
 * Format date to display format (e.g., "Jan 15, 2024")
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDate = (date) => {
  if (!date) return 'N/A';
  
  try {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format time to display format (e.g., "2:30 PM")
 * @param {string|Date} date - Date to extract time from
 * @returns {string} Formatted time string
 */
export const formatTime = (date) => {
  if (!date) return 'N/A';
  
  try {
    const dateObj = new Date(date);
    return dateObj.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'Invalid Time';
  }
};

/**
 * Format date and time together (e.g., "Jan 15, 2024 at 2:30 PM")
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date and time string
 */
export const formatDateTime = (date) => {
  if (!date) return 'N/A';
  
  try {
    const dateObj = new Date(date);
    const formattedDate = dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
    const formattedTime = dateObj.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
    return `${formattedDate} at ${formattedTime}`;
  } catch (error) {
    console.error('Error formatting date and time:', error);
    return 'Invalid Date';
  }
};

/**
 * Format ISIC sector data for display in table
 * @param {Object} sector - Raw sector data from API
 * @returns {Object} Formatted sector data
 */
export const formatIsicSector = (sector) => {
  if (!sector) return null;

  return {
    id: sector.id,
    code: sector.code || 'N/A',
    name: sector.name || 'N/A',
    addedOnDate: sector.addedOnDate,
    addedBy: sector.addedBy || 'N/A',
    // Formatted versions for display
    formattedDate: formatDate(sector.addedOnDate),
    formattedTime: formatTime(sector.addedOnDate),
    formattedDateTime: formatDateTime(sector.addedOnDate),
  };
};

/**
 * Format array of ISIC sectors
 * @param {Array} sectors - Array of raw sector data from API
 * @returns {Array} Array of formatted sector data
 */
export const formatIsicSectors = (sectors) => {
  if (!Array.isArray(sectors)) return [];
  
  return sectors.map(formatIsicSector).filter(Boolean);
};

/**
 * Format currency values
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: 'USD')
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'USD') => {
  if (amount === null || amount === undefined) return 'N/A';
  
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${currency} ${amount}`;
  }
};

/**
 * Format numbers with thousand separators
 * @param {number} number - Number to format
 * @returns {string} Formatted number string
 */
export const formatNumber = (number) => {
  if (number === null || number === undefined) return 'N/A';
  
  try {
    return new Intl.NumberFormat('en-US').format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return number.toString();
  }
};

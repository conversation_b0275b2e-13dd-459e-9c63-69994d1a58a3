import { useState, useEffect } from "react";
import {
  Phone,
  PhoneMissed,
  Mic,
  Mic<PERSON><PERSON>,
  Pause,
  Play,
} from "lucide-react";
import Select from "react-select";
import instance from "../../axios/instance";
import { customerServiceService } from "../../services/customerServiceService";

const CallToDoForm = ({ item, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    callStatus: "",
    customerFeedbackCategory: "",
    notes: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Call state management
  const [isCallActive, setIsCallActive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [callTimer, setCallTimer] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isOnHold, setIsOnHold] = useState(false);

  // Call statuses
  const callStatuses = [
    { value: "success", label: "Success" },
    { value: "no_answer", label: "No Answer" },
  ];

  // Customer feedback categories state
  const [customerFeedbackCategories, setCustomerFeedbackCategories] = useState([]);
  const [feedbackCategoriesLoading, setFeedbackCategoriesLoading] = useState(false);

  // Fetch customer feedback categories
  const fetchFeedbackCategories = async () => {
    try {
      setFeedbackCategoriesLoading(true);
      const response = await customerServiceService.getCustomerFeedbackCategories();
      
      // Convert to React Select format
      const categoryOptions = response.map((category) => ({
        value: category.id,
        label: category.name,
      }));

      setCustomerFeedbackCategories(categoryOptions);
    } catch (error) {
      console.error("Error fetching feedback categories:", error);
      setCustomerFeedbackCategories([]);
    } finally {
      setFeedbackCategoriesLoading(false);
    }
  };

  // Fetch feedback categories on mount
  useEffect(() => {
    fetchFeedbackCategories();
  }, []);

  // Timer effect for call duration
  useEffect(() => {
    let interval;
    if (isCallActive && !isOnHold) {
      interval = setInterval(() => {
        setCallTimer((prev) => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive, isOnHold]);

  // Format timer display
  const formatTimer = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Prepare the call data
      const callData = {
        call_status: formData.callStatus === 'success' ? 'Success' : 'No answer',
        customer_feedback_id: formData.customerFeedbackCategory,
        notes: formData.notes || undefined
      };

      // Add phase only for 2by2by2 type
      if (item.hitlistType === "2by2by2" && item.phase) {
        callData.phase = item.phase.toLowerCase().replace(/\s+/g, '');
      }

      console.log("=== CALL TO DO FORM SUBMISSION ===");
      console.log("Call To Do ID:", item.id);
      console.log("Call Status:", callData.call_status);
      console.log("Customer Feedback ID:", callData.customer_feedback_id);
      console.log("Phase:", callData.phase);
      console.log("Notes:", callData.notes);
      console.log("==================================");

      // Make the API call
      await customerServiceService.makeCall(item.id, callData);

      // Call the onSubmit callback with success
      onSubmit?.(callData, item);
      onClose();
    } catch (error) {
      console.error("Error submitting call:", error);
      onSubmit?.(null, item, error);
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.callStatus) {
      newErrors.callStatus = "Call status is required";
    }

    if (!formData.customerFeedbackCategory) {
      newErrors.customerFeedbackCategory = "Customer feedback category is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Call functionality handlers
  const handleStartCall = async () => {
    setIsConnecting(true);

    try {
      // Simulate API call with 2 second delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      setIsCallActive(true);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to initiate call:", error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      setIsCallActive(false);
      setIsConnecting(false);
      setCallTimer(0);
      setIsMuted(false);
      setIsOnHold(false);
    } catch (error) {
      console.error("Failed to end call:", error);
    }
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleToggleHold = () => {
    setIsOnHold(!isOnHold);
  };

  // React Select styles - matching LeadForm
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused ? "#10b981" : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6">
      {/* Call Section - Always show at the top */}
      <div className="border-b border-gray-200 dark:border-gray-600 min-h-[100px] flex items-center">
        {!isCallActive ? (
          // Call Button - Aligned to left
          <div className="flex justify-start w-full">
            <button
              type="button"
              onClick={handleStartCall}
              disabled={isConnecting}
              className="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-green-400 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm disabled:cursor-not-allowed"
            >
              <Phone size={16} className="mr-2" />
              {isConnecting ? "Calling..." : "Call"}
            </button>
          </div>
        ) : (
          // Call Controls
          <div className="flex justify-between items-center w-full">
            {/* Call Timer */}
            <div className="text-center">
              <div className="text-2xl font-mono text-gray-900 dark:text-white">
                {formatTimer(callTimer)}
              </div>
              <div className="text-sm text-gray-500">Call Duration</div>
            </div>

            <div className="flex flex-col items-center gap-3">
              {/* Call Control Buttons */}
              <div className="flex items-center space-x-3">
                {/* End Call Button */}
                <button
                  type="button"
                  onClick={handleEndCall}
                  className="inline-flex items-center px-4 py-2 font-medium rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: "#f46b68" }}
                  onMouseEnter={(e) =>
                    (e.target.style.backgroundColor = "#e55a57")
                  }
                  onMouseLeave={(e) =>
                    (e.target.style.backgroundColor = "#f46b68")
                  }
                >
                  <PhoneMissed size={14} className="mr-1 text-white" />
                  <span className="text-white">End</span>
                </button>

                {/* Mute/Unmute Button */}
                <button
                  type="button"
                  onClick={handleToggleMute}
                  className={`inline-flex items-center px-4 py-2 font-medium rounded-lg transition-colors duration-200 ${
                    isMuted ? "bg-transparent" : ""
                  }`}
                  style={!isMuted ? { backgroundColor: "#4ade80" } : {}}
                  onMouseEnter={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#22c55e";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isMuted) {
                      e.target.style.backgroundColor = "#4ade80";
                    }
                  }}
                >
                  {isMuted ? (
                    <>
                      <MicOff size={14} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unmute</span>
                    </>
                  ) : (
                    <>
                      <Mic size={14} className="mr-1 text-white" />
                      <span className="text-white">Mute</span>
                    </>
                  )}
                </button>

                {/* Hold/Unhold Button */}
                <button
                  type="button"
                  onClick={handleToggleHold}
                  className={`inline-flex items-center px-4 py-2 font-medium rounded-lg transition-colors duration-200 ${
                    isOnHold ? "bg-transparent" : ""
                  }`}
                  style={!isOnHold ? { backgroundColor: "#ffb800" } : {}}
                  onMouseEnter={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#e6a600";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isOnHold) {
                      e.target.style.backgroundColor = "#ffb800";
                    }
                  }}
                >
                  {isOnHold ? (
                    <>
                      <Play size={14} className="mr-1 text-gray-500" />
                      <span className="text-gray-500">Unhold</span>
                    </>
                  ) : (
                    <>
                      <Pause size={14} className="mr-1 text-white" />
                      <span className="text-white">Hold</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Call Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Call Status *
          </label>
          <Select
            value={callStatuses.find(
              (status) => status.value === formData.callStatus
            )}
            onChange={(selectedOption) =>
              setFormData((prev) => ({
                ...prev,
                callStatus: selectedOption?.value || "",
              }))
            }
            options={callStatuses}
            styles={selectStyles}
            placeholder="Select call status"
            isSearchable={false}
          />
          {errors.callStatus && (
            <p className="mt-1 text-sm text-red-600">{errors.callStatus}</p>
          )}
        </div>

        {/* Customer Feedback Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Customer Feedback Category *
          </label>
          <Select
            value={customerFeedbackCategories.find(
              (category) => category.value === formData.customerFeedbackCategory
            )}
            onChange={(selectedOption) =>
              setFormData((prev) => ({
                ...prev,
                customerFeedbackCategory: selectedOption?.value || "",
              }))
            }
            options={customerFeedbackCategories}
            styles={selectStyles}
            placeholder="Select customer feedback category"
            isSearchable={true}
            isLoading={feedbackCategoriesLoading}
            isDisabled={feedbackCategoriesLoading}
          />
          {errors.customerFeedbackCategory && (
            <p className="mt-1 text-sm text-red-600">{errors.customerFeedbackCategory}</p>
          )}
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notes
          </label>
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleInputChange}
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
            placeholder="Add any notes about the call..."
          />
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 bg-[#165026] hover:bg-green-700 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? "Saving..." : "Save Call"}
        </button>
      </div>
    </form>
  );
};

export default CallToDoForm;
import instance from '../axios/instance.jsx';

// Visits API endpoints
const ENDPOINTS = {
  VISITS: '/visits',
  VISIT_BY_ID: (id) => `/visits/${id}`,
};

// Visits Service
export const visitsService = {
  // Get all visits
  getAll: async () => {
    try {
      console.log('=== FETCHING ALL VISITS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.VISITS}`);
      
      const response = await instance.get(ENDPOINTS.VISITS);
      
      console.log('Visits response:', response.data);
      console.log('===============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching visits:', error);
      throw error;
    }
  },

  // Create a new visit
  create: async (visitData) => {
    try {
      console.log('=== CREATING NEW VISIT ===');
      console.log('Visit data:', visitData);
      console.log(`API Endpoint: POST ${ENDPOINTS.VISITS}`);
      
      const response = await instance.post(ENDPOINTS.VISITS, visitData);
      
      console.log('Visit creation response:', response.data);
      console.log('==============================');
      
      return response.data;
    } catch (error) {
      console.error('Error creating visit:', error);
      
      // Handle different error types
      if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid visit data provided.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to create visits.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while creating visit. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to create visit.');
      }
    }
  },

  // Update an existing visit
  update: async (id, visitData) => {
    try {
      console.log('=== UPDATING VISIT ===');
      console.log(`Visit ID: ${id}`);
      console.log('Updated data:', visitData);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.VISIT_BY_ID(id)}`);
      
      const response = await instance.patch(ENDPOINTS.VISIT_BY_ID(id), visitData);
      
      console.log('Visit update response:', response.data);
      console.log('======================');
      
      return response.data;
    } catch (error) {
      console.error('Error updating visit:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Visit not found. It may have been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this visit.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid visit data provided.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while updating visit. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update visit.');
      }
    }
  },

  // Delete a visit
  delete: async (id) => {
    try {
      console.log('=== DELETING VISIT ===');
      console.log(`Visit ID: ${id}`);
      console.log(`API Endpoint: DELETE ${ENDPOINTS.VISIT_BY_ID(id)}`);
      
      const response = await instance.delete(ENDPOINTS.VISIT_BY_ID(id));
      
      console.log('Visit deletion response:', response.status);
      console.log('======================');
      
      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting visit:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Visit not found. It may have already been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this visit.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while deleting visit. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to delete visit.');
      }
    }
  },

  // Get a single visit by ID
  getById: async (id) => {
    try {
      console.log('=== FETCHING VISIT BY ID ===');
      console.log(`Visit ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.VISIT_BY_ID(id)}`);
      
      const response = await instance.get(ENDPOINTS.VISIT_BY_ID(id));
      
      console.log('Visit details response:', response.data);
      console.log('============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching visit by ID:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Visit not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view this visit.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching visit. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch visit details.');
      }
    }
  },
};

/**
 * Format visits data for table display
 * @param {Object} apiResponse - Response from visits API
 * @returns {Array} - Formatted data for DataTable
 */
export const formatVisitsForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((visit, index) => ({
    id: visit.id || `visit${index + 1}`,
    name: visit.lead_name || visit.customer_name || "Unknown Lead",
    anchor: visit.performed_by?.name || visit.anchor_name || "Unknown",
    mobile: visit.lead_phone || visit.phone_number || "No phone",
    madeBy: visit.performed_by?.name || visit.created_by || "Unknown",
    status: visit.visit_status || visit.status || "Unknown",
    date: formatVisitDate(visit.created_at || visit.visit_date),
    // Additional fields for detailed view
    duration: visit.visit_duration_minutes || visit.duration,
    notes: visit.notes || visit.description,
    purpose: visit.purpose?.name || visit.visit_purpose,
    nextFollowup: visit.next_followup_date,
    leadId: visit.lead_id,
    performedBy: visit.performed_by,
    createdAt: visit.created_at,
    location: visit.location || visit.visit_location,
  }));
};

/**
 * Format visit date for display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
const formatVisitDate = (dateString) => {
  if (!dateString) return "Unknown date";
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (error) {
    console.warn('Error formatting visit date:', dateString);
    return "Invalid date";
  }
};

export default visitsService;

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import Select from "react-select";
import instance from "../../axios/instance";

const TargetForm = ({ onClose, onSubmit, item = null }) => {
  const [formData, setFormData] = useState({
    metricType: "",
    targetValue: "",
    frequency: "",
    startDate: "",
    endDate: "",
    scope: "",
    assignTo: [],
  });
  const [originalData, setOriginalData] = useState(null);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [roles, setRoles] = useState([]);
  const [users, setUsers] = useState([]);
  const [loadingOptions, setLoadingOptions] = useState(false);

  // Users will be fetched from API

  // Metric type options
  const metricOptions = [
    { value: "Call", label: "Call" },
    { value: "Visit", label: "Visit" },
  ];

  // Frequency options
  const frequencyOptions = [
    { value: "daily", label: "Daily" },
    { value: "weekly", label: "Weekly" },
    { value: "custom", label: "Custom" },
  ];

  // Scope options
  const scopeOptions = [
    { value: "Role", label: "Role" },
    { value: "Individual", label: "Individual" },
  ];

  // Initialize form data when editing
  useEffect(() => {
    if (item) {
      // Extract assignTo IDs based on scope
      let assignToIds = [];
      if (item.scope === "role") {
        // For role targets, assigned_to is a string (role name)
        // We'll need to find the role ID from the roles list
        assignToIds = []; // Will be populated after roles are fetched
      } else if (item.scope === "individual") {
        // For individual targets, assigned_to is an object or array of objects
        if (Array.isArray(item.assigned_to)) {
          assignToIds = item.assigned_to.map((user) => user.id);
        } else if (
          typeof item.assigned_to === "object" &&
          item.assigned_to.id
        ) {
          assignToIds = [item.assigned_to.id];
        }
      }

      const editFormData = {
        metricType: item.metric || "",
        targetValue: item.value?.toString() || "",
        frequency: item.frequency || "",
        startDate: item.start_date || "",
        endDate: item.end_date || "",
        scope: item.scope === "role" ? "Role" : "Individual",
        assignTo: assignToIds,
      };

      setFormData(editFormData);
      setOriginalData(editFormData);
    }
  }, [item]);

  // Fetch roles or users based on scope
  useEffect(() => {
    if (formData.scope === "Role") {
      fetchRoles();
    } else if (formData.scope === "Individual") {
      fetchUsers();
    }
  }, [formData.scope]);

  const fetchRoles = async () => {
    try {
      setLoadingOptions(true);
      const response = await instance.get("/roles");
      const roleOptions = response.data.data.map((role) => ({
        value: role.id,
        label: role.name,
      }));
      setRoles(roleOptions);
    } catch (error) {
      console.error("Error fetching roles:", error);
      setRoles([]);
    } finally {
      setLoadingOptions(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoadingOptions(true);
      const response = await instance.get("/users");
      const userOptions = response.data.data.map((user) => ({
        value: user.id,
        label: user.name,
      }));
      setUsers(userOptions);
    } catch (error) {
      console.error("Error fetching users:", error);
      setUsers([]);
    } finally {
      setLoadingOptions(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleSelectChange = (name, selectedOptions) => {
    if (name === "assignTo") {
      const values = selectedOptions
        ? selectedOptions.map((option) => option.value)
        : [];
      setFormData((prev) => ({
        ...prev,
        [name]: values,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: selectedOptions ? selectedOptions.value : "",
      }));
    }
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.metricType) {
      newErrors.metricType = "Metric type is required";
    }
    if (!formData.targetValue || formData.targetValue <= 0) {
      newErrors.targetValue = "Target value must be greater than 0";
    }
    if (!formData.frequency) {
      newErrors.frequency = "Frequency is required";
    }
    if (!formData.startDate) {
      newErrors.startDate = "Start date is required";
    }
    if (!formData.endDate) {
      newErrors.endDate = "End date is required";
    }
    if (
      formData.startDate &&
      formData.endDate &&
      new Date(formData.startDate) >= new Date(formData.endDate)
    ) {
      newErrors.endDate = "End date must be after start date";
    }
    // Only validate scope and assignTo when creating
    if (!item) {
      if (!formData.scope) {
        newErrors.scope = "Scope is required";
      }
      if (formData.assignTo.length === 0) {
        newErrors.assignTo = "At least one assignment is required";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      let targetData = {
        metricType: formData.metricType,
        targetValue: parseInt(formData.targetValue),
        frequency: formData.frequency,
        startDate: formData.startDate,
        endDate: formData.endDate,
      };

      // For creating, include scope and assignTo
      if (!item) {
        targetData.scope = formData.scope.toLowerCase();
        targetData.assignTo = formData.assignTo;
      }
      // For editing, don't include scope and assignTo

      console.log("Target data to be submitted:", targetData);

      // Call the onSubmit callback with the data and item ID for editing
      await onSubmit(targetData, item?.id);

      // Reset form and close modal
      setFormData({
        metricType: "",
        targetValue: "",
        frequency: "",
        startDate: "",
        endDate: "",
        scope: "",
        assignTo: [],
      });
      onClose();
    } catch (error) {
      console.error("Error submitting target:", error);
      setErrors({
        submit: item
          ? "Failed to update target. Please try again."
          : "Failed to create target. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Get assign to options based on scope
  const getAssignToOptions = () => {
    if (formData.scope === "Role") {
      return roles;
    } else if (formData.scope === "Individual") {
      return users;
    }
    return [];
  };

  const getSelectedAssignTo = () => {
    const options = getAssignToOptions();
    return options.filter((option) => formData.assignTo.includes(option.value));
  };

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Metric Type */}
        <div>
          <label
            className="block text-sm font-medium mb-2"
            style={{ color: "#7e7e7e" }}
          >
            Metric Type
          </label>
          <Select
            options={metricOptions}
            value={metricOptions.find(
              (option) => option.value === formData.metricType
            )}
            onChange={(selectedOption) =>
              handleSelectChange("metricType", selectedOption)
            }
            placeholder="Select metric type"
            className="react-select-container"
            classNamePrefix="react-select"
            styles={{
              control: (base, state) => ({
                ...base,
                borderColor: errors.metricType ? "#ef4444" : "#d1d5db",
                boxShadow: "none",
                "&:hover": {
                  borderColor: errors.metricType ? "#ef4444" : "#9ca3af",
                },
              }),
            }}
          />
          {errors.metricType && (
            <p className="text-red-500 text-sm mt-1">{errors.metricType}</p>
          )}
        </div>

        {/* Target Value */}
        <div>
          <label
            className="block text-sm font-medium mb-2"
            style={{ color: "#7e7e7e" }}
          >
            Target Value
          </label>
          <input
            type="number"
            name="targetValue"
            value={formData.targetValue}
            onChange={handleChange}
            placeholder="Enter target value"
            min="1"
            className={`w-full px-3 py-2 border rounded-lg text-sm ${
              errors.targetValue ? "border-red-500" : "border-gray-300"
            } focus:border-gray-400 transition-colors duration-200`}
            style={{ outline: "none", boxShadow: "none" }}
          />
          {errors.targetValue && (
            <p className="text-red-500 text-sm mt-1">{errors.targetValue}</p>
          )}
        </div>

        {/* Frequency */}
        <div>
          <label
            className="block text-sm font-medium mb-2"
            style={{ color: "#7e7e7e" }}
          >
            Frequency
          </label>
          <Select
            options={frequencyOptions}
            value={frequencyOptions.find(
              (option) => option.value === formData.frequency
            )}
            onChange={(selectedOption) =>
              handleSelectChange("frequency", selectedOption)
            }
            placeholder="Select frequency"
            className="react-select-container"
            classNamePrefix="react-select"
            styles={{
              control: (base, state) => ({
                ...base,
                borderColor: errors.frequency ? "#ef4444" : "#d1d5db",
                boxShadow: "none",
                "&:hover": {
                  borderColor: errors.frequency ? "#ef4444" : "#9ca3af",
                },
              }),
            }}
          />
          {errors.frequency && (
            <p className="text-red-500 text-sm mt-1">{errors.frequency}</p>
          )}
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "#7e7e7e" }}
            >
              Start Date
            </label>
            <input
              type="date"
              name="startDate"
              value={formData.startDate}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-lg text-sm ${
                errors.startDate ? "border-red-500" : "border-gray-300"
              } focus:border-gray-400 transition-colors duration-200`}
              style={{ outline: "none", boxShadow: "none" }}
            />
            {errors.startDate && (
              <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>
            )}
          </div>
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "#7e7e7e" }}
            >
              End Date
            </label>
            <input
              type="date"
              name="endDate"
              value={formData.endDate}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-lg text-sm ${
                errors.endDate ? "border-red-500" : "border-gray-300"
              } focus:border-gray-400 transition-colors duration-200`}
              style={{ outline: "none", boxShadow: "none" }}
            />
            {errors.endDate && (
              <p className="text-red-500 text-sm mt-1">{errors.endDate}</p>
            )}
          </div>
        </div>

        {/* Scope - Only show when creating */}
        {!item && (
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "#7e7e7e" }}
            >
              Scope
            </label>
            <Select
              options={scopeOptions}
              value={scopeOptions.find(
                (option) => option.value === formData.scope
              )}
              onChange={(selectedOption) =>
                handleSelectChange("scope", selectedOption)
              }
              placeholder="Select scope"
              className="react-select-container"
              classNamePrefix="react-select"
              styles={{
                control: (base, state) => ({
                  ...base,
                  borderColor: errors.scope ? "#ef4444" : "#d1d5db",
                  boxShadow: "none",
                  "&:hover": {
                    borderColor: errors.scope ? "#ef4444" : "#9ca3af",
                  },
                }),
              }}
            />
            {errors.scope && (
              <p className="text-red-500 text-sm mt-1">{errors.scope}</p>
            )}
          </div>
        )}

        {/* Assign To - Only show when creating */}
        {!item && (
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{ color: "#7e7e7e" }}
            >
              Assign To
            </label>
            <Select
              isMulti
              options={getAssignToOptions()}
              value={getSelectedAssignTo()}
              onChange={(selectedOptions) =>
                handleSelectChange("assignTo", selectedOptions)
              }
              placeholder={
                formData.scope === "Role"
                  ? "Select roles"
                  : formData.scope === "Individual"
                  ? "Select users"
                  : "Select scope first"
              }
              isDisabled={!formData.scope}
              isLoading={loadingOptions}
              className="react-select-container"
              classNamePrefix="react-select"
              styles={{
                control: (base, state) => ({
                  ...base,
                  borderColor: errors.assignTo ? "#ef4444" : "#d1d5db",
                  boxShadow: "none",
                  "&:hover": {
                    borderColor: errors.assignTo ? "#ef4444" : "#9ca3af",
                  },
                }),
              }}
            />
            {errors.assignTo && (
              <p className="text-red-500 text-sm mt-1">{errors.assignTo}</p>
            )}
          </div>
        )}

        {/* Submit Error */}
        {errors.submit && (
          <div className="text-red-500 text-sm">{errors.submit}</div>
        )}

        {/* Buttons */}
        <div className="flex gap-3 pt-4">
          <button
            type="button"
            onClick={handleClose}
            disabled={isSubmitting}
            className="flex-1 px-4 py-3 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ color: "#7e7e7e" }}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 px-4 py-3 text-sm font-medium text-white rounded-lg bg-green-600 hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Creating...
              </>
            ) : item ? (
              "Update Target"
            ) : (
              "Create Target"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TargetForm;

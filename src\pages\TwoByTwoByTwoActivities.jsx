import { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import PrivateLayout from "../components/layouts/PrivateLayout";
import TabHeader from "../components/activities/2x2x2/components/TabHeader";
import HitlistTab from "../components/activities/2x2x2/tabs/HitlistTab";
import OtherTab from "../components/activities/2x2x2/tabs/OtherTab";

const TwoByTwoByTwoActivities = () => {
  const { type } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Default to 'hitlist' if no type is specified
  const activeTab = type || "hitlist";

  // Redirect to hitlist tab if accessing base route
  useEffect(() => {
    if (location.pathname === "/customer-service/2by2by2") {
      navigate("/customer-service/2by2by2/hitlist", { replace: true });
    }
  }, [location.pathname, navigate]);

  // Tab configuration
  const tabs = [
    {
      id: "hitlist",
      label: "Hitlist",
      path: "/customer-service/2by2by2/hitlist",
    },
    {
      id: "first2",
      label: "First 2",
      path: "/customer-service/2by2by2/first2",
    },
    {
      id: "second2",
      label: "Second 2",
      path: "/customer-service/2by2by2/second2",
    },
    {
      id: "third2",
      label: "Third 2",
      path: "/customer-service/2by2by2/third2",
    },
  ];

  // Handle tab change
  const handleTabChange = (tabId) => {
    const tab = tabs.find((t) => t.id === tabId);
    if (tab) {
      navigate(tab.path);
    }
  };

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    console.log("View item:", item);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more 2x2x2 activities");
    }, 2000);
  };

  // Custom header content with tabs
  const customHeaderContent = (
    <TabHeader
      tabs={tabs}
      activeTab={activeTab}
      onTabChange={handleTabChange}
    />
  );

  return (
    <PrivateLayout>
      <div className="">
        {activeTab === "hitlist" && (
          <HitlistTab
            loading={loading}
            loadingMore={loadingMore}
            onView={handleView}
            onLoadMore={handleLoadMore}
            customHeaderContent={customHeaderContent}
          />
        )}

        {(activeTab === "first2" ||
          activeTab === "second2" ||
          activeTab === "third2") && (
          <OtherTab
            tabId={activeTab}
            loading={loading}
            loadingMore={loadingMore}
            onView={handleView}
            onLoadMore={handleLoadMore}
            customHeaderContent={customHeaderContent}
          />
        )}
      </div>
    </PrivateLayout>
  );
};

export default TwoByTwoByTwoActivities;

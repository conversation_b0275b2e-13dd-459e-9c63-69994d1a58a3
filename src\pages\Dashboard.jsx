import PrivateLayout from "../components/layouts/PrivateLayout";
import BankerDashboard from "../components/dashboards/BankerDashboard";
import BranchManagerDashboard from "../components/dashboards/BranchManagerDashboard";
import CustomerServiceDashboard from "../components/dashboards/CustomerServiceDashboard";

function Dashboard() {
  // Dashboard type switcher - manually change this value to switch between dashboards
  // Options: "banker", "branch-manager", "customer-service"
  const dashboardType = "branch-manager";

  const renderDashboardContent = () => {
    switch (dashboardType) {
      case "banker":
        return <BankerDashboard />;
      case "branch-manager":
        return <BranchManagerDashboard />;
      case "customer-service":
        return <CustomerServiceDashboard />;
      default:
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
            <p>Please select a valid dashboard type.</p>
          </div>
        );
    }
  };

  return <PrivateLayout>{renderDashboardContent()}</PrivateLayout>;
}

export default Dashboard;

import React, { useRef, useEffect, useState } from "react";
import DashboardCard from "../cards/DashboardCard";
import OverviewCharts from "../charts/OverviewCharts";
import PerformanceCharts from "../charts/PerformanceCharts";
import ActivitiesTable from "../tables/ActivitiesTable";
import useCountUp from "../../hooks/useCountUp";

function BranchManagerDashboard() {
  // Animation state for Activities Overview
  const [hasAnimatedActivities, setHasAnimatedActivities] = useState(false);
  const [shouldAnimateActivities, setShouldAnimateActivities] = useState(false);
  const activitiesRef = useRef(null);

  // Dashboard data variables
  const hitlistSize = 247;
  const hitlistDescription = "Total prospects";

  const customersContacted = 89;
  const contactedPercentage = 36;
  const contactedDescription = "36% of hitlist";

  const upcomingActivities = 3;
  const activitiesDescription = "Scheduled for today/tomorrow";

  const overdueItems = 2;
  const overdueDescription = "Require immediate attention";

  // Activities data for animation
  const upcomingCount = 2;
  const scheduledCount = 3;
  const overdueCount = 3;

  // Intersection Observer for Activities Overview
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimatedActivities) {
            setShouldAnimateActivities(true);
            setHasAnimatedActivities(true);
          }
        });
      },
      { threshold: 0.3 }
    );

    if (activitiesRef.current) {
      observer.observe(activitiesRef.current);
    }

    return () => {
      if (activitiesRef.current) {
        observer.unobserve(activitiesRef.current);
      }
    };
  }, [hasAnimatedActivities]);

  // Animated values for Activities Overview
  const animatedUpcoming = useCountUp(
    shouldAnimateActivities ? upcomingCount : 0,
    1000,
    0
  );
  const animatedScheduled = useCountUp(
    shouldAnimateActivities ? scheduledCount : 0,
    1000,
    150
  );
  const animatedOverdue = useCountUp(
    shouldAnimateActivities ? overdueCount : 0,
    1000,
    300
  );

  // Icon components
  const HitlistIcon = (
    <svg
      className="w-7 h-7 text-[#F47976]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </svg>
  );

  const ActivitiesIcon = (
    <svg
      className="w-7 h-7 text-[#1c5b41]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  );

  const OverdueIcon = (
    <svg
      className="w-7 h-7 text-[#FDCA37]"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
      />
    </svg>
  );

  return (
    <div className="p-6">
      <h1 className="text-2xl font-medium mb-6">Branch Manager Dashboard</h1>

      {/* Top Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <DashboardCard
          title="Hitlist Size"
          value={hitlistSize}
          description={hitlistDescription}
          icon={HitlistIcon}
          iconColor="#F47976"
          duration={2000}
          delay={200}
        />

        <DashboardCard
          title="Upcoming Activities"
          value={upcomingActivities}
          description={activitiesDescription}
          icon={ActivitiesIcon}
          iconColor="#1c5b41"
          duration={1500}
          delay={400}
        />

        <DashboardCard
          title="Overdue Items"
          value={overdueItems}
          description={overdueDescription}
          icon={OverdueIcon}
          iconColor="#FDCA37"
          duration={1000}
          delay={600}
        />
      </div>

      {/* Overview Charts Section */}
      <div className="mb-8">
        <OverviewCharts />
      </div>

      {/* Performance Charts Section */}
      <div className="mb-8">
        <PerformanceCharts />
      </div>

      {/* Activities Statistics Section */}
      <div
        ref={activitiesRef}
        className="bg-white rounded-xl shadow-lg p-6 mb-8"
      >
        <div className="mb-6">
          <h3 className="text-[23px] font-semibold text-gray-800 mb-2">
            ACTIVITIES OVERVIEW
          </h3>
          <p className="text-sm text-gray-600" style={{ fontSize: "15px" }}>
            Summary of upcoming, scheduled and overdue activities
          </p>
        </div>

        {/* Stats Summary */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold" style={{ color: "#1E3A8A" }}>
              {animatedUpcoming}
            </div>
            <div className="text-sm text-gray-600">Upcoming</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold" style={{ color: "#10b981" }}>
              {animatedScheduled}
            </div>
            <div className="text-sm text-gray-600">Scheduled</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold" style={{ color: "#ef4444" }}>
              {animatedOverdue}
            </div>
            <div className="text-sm text-gray-600">Overdue</div>
          </div>
        </div>
      </div>

      {/* Activities Table Section */}
      <ActivitiesTable />
    </div>
  );
}

export default BranchManagerDashboard;

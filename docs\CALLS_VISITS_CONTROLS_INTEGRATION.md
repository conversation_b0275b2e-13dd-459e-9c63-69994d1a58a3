# Calls and Visits Controls Integration Documentation

## Overview

This document describes the integration of complete CRUD controls, dialogs, and API logic from the Hitlist.jsx page to both the Calls and Visits tables. The implementation includes create, edit, delete, call, and visit operations with proper API integration and state management.

## Features Implemented

### 🚀 **Core Features**
- **Complete CRUD Operations**: Create, Read, Update, Delete for calls and visits
- **Action Controls**: Call, Visit, Edit, Delete actions in dropdown menus
- **Modal Dialogs**: Proper modal forms for all operations
- **API Integration**: Real backend API calls for all operations
- **State Management**: Optimistic updates and error handling
- **Toast Notifications**: User feedback for all operations

### 🎯 **User Experience Features**
- **Consistent UI**: Same controls and behavior as Hitlist page
- **Loading States**: Visual feedback during operations
- **Error Handling**: Comprehensive error messages
- **Success Feedback**: Toast notifications for successful operations
- **Responsive Design**: Works on all screen sizes

## Implementation Details

### **Files Created/Modified**

#### **New Files:**
1. **`src/services/callsService.js`** - Complete calls API service with CRUD operations
2. **`src/services/visitsService.js`** - Complete visits API service with CRUD operations
3. **`docs/CALLS_VISITS_CONTROLS_INTEGRATION.md`** - This documentation

#### **Modified Files:**
1. **`src/pages/Calls.jsx`** - Added complete CRUD controls and API integration
2. **`src/pages/Visits.jsx`** - Added complete CRUD controls and API integration

### **API Integration**

#### **Calls Service Endpoints:**
```
GET    /api/v1/calls           - Get all calls
POST   /api/v1/calls           - Create new call
GET    /api/v1/calls/:id       - Get call by ID
PATCH  /api/v1/calls/:id       - Update call
DELETE /api/v1/calls/:id       - Delete call
```

#### **Visits Service Endpoints:**
```
GET    /api/v1/visits          - Get all visits
POST   /api/v1/visits          - Create new visit
GET    /api/v1/visits/:id      - Get visit by ID
PATCH  /api/v1/visits/:id      - Update visit
DELETE /api/v1/visits/:id      - Delete visit
```

#### **Service Implementation:**
```javascript
// Example from callsService.js
export const callsService = {
  getAll: async () => {
    const response = await instance.get('/calls');
    return response.data;
  },

  create: async (callData) => {
    const response = await instance.post('/calls', callData);
    return response.data;
  },

  update: async (id, callData) => {
    const response = await instance.patch(`/calls/${id}`, callData);
    return response.data;
  },

  delete: async (id) => {
    const response = await instance.delete(`/calls/${id}`);
    return response.status === 204 || response.status === 200;
  },
};
```

### **Component Architecture**

#### **Action Controls:**
```javascript
// Actions available in dropdown
actions={["call", "visit", "edit", "delete"]}
```

#### **CRUD Handlers:**
```javascript
// Create handler
const handleCreateSubmit = async (formData) => {
  try {
    const newCall = await callsService.create(formData);
    setCalls(prevCalls => [newCall, ...prevCalls]);
    toast.success("Call created successfully!");
  } catch (error) {
    toast.error(error.message || "Failed to create call");
  }
};

// Update handler
const handleEditSubmit = async (updatedData, originalItem) => {
  try {
    const updatedCall = await callsService.update(originalItem.id, updatedData);
    setCalls(prevCalls => 
      prevCalls.map(call => 
        call.id === originalItem.id ? { ...call, ...updatedCall } : call
      )
    );
    toast.success("Call updated successfully!");
  } catch (error) {
    toast.error(error.message || "Failed to update call");
  }
};

// Delete handler
const handleDeleteConfirm = async (call) => {
  try {
    const success = await callsService.delete(call.id);
    if (success) {
      setCalls(prevCalls => prevCalls.filter(c => c.id !== call.id));
      toast.success("Call deleted successfully!");
    }
  } catch (error) {
    toast.error(error.message || "Failed to delete call");
  }
};
```

#### **Modal Forms:**
```javascript
// Modal form configurations
createForm={({ onClose }) => (
  <CallForm onClose={onClose} onSubmit={handleCreateSubmit} />
)}
editForm={({ item, onClose }) => (
  <CallForm
    item={item}
    onClose={onClose}
    onSubmit={handleEditSubmit}
  />
)}
deleteForm={({ item, onClose }) => (
  <DeleteConfirmation
    item={item}
    onClose={onClose}
    onConfirm={handleDeleteConfirm}
    itemName="Call"
  />
)}
callForm={({ item, onClose }) => (
  <CallForm
    item={item}
    onClose={onClose}
    onSubmit={handleCallSubmit}
  />
)}
visitForm={({ item, onClose }) => (
  <VisitForm
    item={item}
    onClose={onClose}
    onSubmit={handleVisitSubmit}
  />
)}
```

### **State Management**

#### **Optimistic Updates:**
```javascript
// Immediate UI update before API call
setCalls(prevCalls => [newCall, ...prevCalls]);

// Update specific item in array
setCalls(prevCalls => 
  prevCalls.map(call => 
    call.id === targetId ? { ...call, ...updatedData } : call
  )
);

// Remove item from array
setCalls(prevCalls => prevCalls.filter(c => c.id !== targetId));
```

#### **Cross-Activity Updates:**
```javascript
// Update call count when new call is made
const handleCallSubmit = async (callData, item, error) => {
  if (!error && callData && item) {
    setCalls(prevCalls => 
      prevCalls.map(c => 
        c.id === item.id 
          ? { 
              ...c, 
              calls: (parseInt(c.calls) || 0) + 1,
              lastInteraction: new Date().toISOString().split('T')[0],
              lastInteractionType: "call"
            }
          : c
      )
    );
  }
};
```

### **Error Handling**

#### **Service Level Error Handling:**
```javascript
// Comprehensive error handling in services
try {
  const response = await instance.post('/calls', callData);
  return response.data;
} catch (error) {
  if (error.response?.status === 400) {
    throw new Error('Invalid call data provided.');
  } else if (error.response?.status === 403) {
    throw new Error('You do not have permission to create calls.');
  } else if (error.response?.status >= 500) {
    throw new Error('Server error. Please try again later.');
  } else {
    throw new Error(error.response?.data?.message || 'Failed to create call.');
  }
}
```

#### **Component Level Error Handling:**
```javascript
// User-friendly error feedback
try {
  await callsService.create(formData);
  toast.success("Call created successfully!");
} catch (error) {
  console.error("Error creating call:", error);
  toast.error(error.message || "Failed to create call");
}
```

### **Data Formatting**

#### **Service Data Formatting:**
```javascript
export const formatCallsForTable = (apiResponse) => {
  return apiResponse.data.map((call, index) => ({
    id: call.id || `call${index + 1}`,
    name: call.lead_name || "Unknown Lead",
    anchor: call.performed_by?.name || "Unknown",
    mobile: call.lead_phone || "No phone",
    madeBy: call.performed_by?.name || "Unknown",
    status: call.call_status || "Unknown",
    date: formatCallDate(call.created_at),
    // Additional fields for detailed operations
    duration: call.call_duration_minutes,
    notes: call.notes,
    purpose: call.purpose?.name,
    leadId: call.lead_id,
    performedBy: call.performed_by,
  }));
};
```

## Usage Instructions

### **For Users:**

#### **Creating New Calls/Visits:**
1. Click "New Call" or "New Visit" button
2. Fill out the form in the modal
3. Click "Save" to create
4. Success message appears and table updates

#### **Editing Calls/Visits:**
1. Click actions dropdown (⋮) on any row
2. Select "Edit"
3. Modify data in the form
4. Click "Save" to update
5. Success message appears and table updates

#### **Deleting Calls/Visits:**
1. Click actions dropdown (⋮) on any row
2. Select "Delete"
3. Confirm deletion in the dialog
4. Success message appears and item is removed

#### **Making Calls/Visits:**
1. Click actions dropdown (⋮) on any row
2. Select "Call" or "Visit"
3. Fill out the activity form
4. Click "Save" to log the activity
5. Related counters update in the table

### **For Developers:**

#### **Adding New Actions:**
```javascript
// 1. Add action to actions array
actions={["call", "visit", "edit", "delete", "new-action"]}

// 2. Add handler function
const handleNewAction = async (item) => {
  // Implementation
};

// 3. Add modal form if needed
newActionForm={({ item, onClose }) => (
  <NewActionForm
    item={item}
    onClose={onClose}
    onSubmit={handleNewAction}
  />
)}
```

#### **Customizing API Endpoints:**
```javascript
// Update service endpoints
const ENDPOINTS = {
  CALLS: '/api/v2/calls',  // Updated endpoint
  CALL_BY_ID: (id) => `/api/v2/calls/${id}`,
};
```

#### **Adding Custom Fields:**
```javascript
// Update data formatting
export const formatCallsForTable = (apiResponse) => {
  return apiResponse.data.map((call) => ({
    // ... existing fields
    customField: call.custom_field || "Default value",
  }));
};

// Update column definitions
const columns = [
  // ... existing columns
  {
    key: "customField",
    title: "CUSTOM FIELD",
    render: (value) => <span>{value}</span>,
  },
];
```

## Testing

### **Manual Testing Checklist:**
- [ ] Create new call/visit works
- [ ] Edit call/visit works
- [ ] Delete call/visit works
- [ ] Call action from dropdown works
- [ ] Visit action from dropdown works
- [ ] Toast notifications appear
- [ ] Table updates immediately
- [ ] Error handling works
- [ ] Modal forms open/close properly

### **Test Scenarios:**
1. **CRUD Operations**: Test all create, read, update, delete operations
2. **Cross-Activity Updates**: Test call/visit actions update counters
3. **Error Handling**: Test with network errors and invalid data
4. **Concurrent Operations**: Test multiple rapid operations
5. **Data Validation**: Test form validation and error messages

## Performance Considerations

### **Optimizations:**
- **Optimistic Updates**: Immediate UI feedback
- **Efficient State Updates**: Minimal re-renders
- **Error Recovery**: Graceful error handling
- **Toast Notifications**: Non-blocking user feedback

### **Limitations:**
- **No Pagination**: All data loaded at once
- **No Real-time Sync**: Manual refresh required
- **Memory Usage**: Large datasets may impact performance

## Future Enhancements

### **Planned Features:**
1. **Bulk Operations**: Select and operate on multiple items
2. **Advanced Filters**: Filter by status, date, etc.
3. **Export Functionality**: Export calls/visits data
4. **Real-time Updates**: WebSocket integration
5. **Audit Trail**: Track all changes

### **Technical Improvements:**
1. **Pagination**: Load data in chunks
2. **Caching**: Client-side data caching
3. **Optimistic Locking**: Prevent concurrent edit conflicts
4. **Background Sync**: Sync data in background

## Support

For technical support or feature requests regarding the calls and visits controls:
1. Check browser console for detailed error logs
2. Verify API endpoint availability and permissions
3. Test with different data scenarios
4. Contact development team with specific error messages

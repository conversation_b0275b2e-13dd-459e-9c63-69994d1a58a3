import { useState, useEffect } from "react";
import DataTable from "../../../common/DataTable";
import { otherTabsColumns } from "../config/columns.jsx";
import { allActivities } from "../data/sampleData";

const OtherTab = ({
  tabId,
  loading,
  loadingMore,
  onView,
  onLoadMore,
  customHeaderContent,
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every 30 seconds for live countdown
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(timer);
  }, []);

  // Get tab-specific data and labels
  const getTabData = () => {
    switch (tabId) {
      case "first2":
        return {
          data: allActivities.first2,
          label: "First 2",
          searchPlaceholder: "Search first 2...",
          loadMoreText: "Load More First 2 Activities",
        };
      case "second2":
        return {
          data: allActivities.second2,
          label: "Second 2",
          searchPlaceholder: "Search second 2...",
          loadMoreText: "Load More Second 2 Activities",
        };
      case "third2":
        return {
          data: allActivities.third2,
          label: "Third 2",
          searchPlaceholder: "Search third 2...",
          loadMoreText: "Load More Third 2 Activities",
        };
      default:
        return {
          data: [],
          label: "Unknown",
          searchPlaceholder: "Search...",
          loadMoreText: "Load More Activities",
        };
    }
  };

  const tabData = getTabData();

  // Create columns with current time for countdown
  const columnsWithTime = otherTabsColumns.map((column) => ({
    ...column,
    render: column.render
      ? (value, item) => column.render(value, item, currentTime)
      : column.render,
  }));

  return (
    <DataTable
      key={`${tabId}-table`}
      columns={columnsWithTime}
      data={tabData.data}
      searchPlaceholder={tabData.searchPlaceholder}
      onView={onView}
      actions={["edit", "delete"]}
      loading={loading}
      loadingMore={loadingMore}
      loadMoreText={tabData.loadMoreText}
      onLoadMore={onLoadMore}
      showLoadMore={true}
      dataCountLabel={tabId}
      customHeaderContent={customHeaderContent}
      modalSize="lg"
      // No create button for other tabs
      showAddButton={false}
    />
  );
};

export default OtherTab;

import React from 'react';

/*
  DashboardSection Component: wrapper for dashboard sections with consistent styling.
  Props:
    - title: optional section title
    - children: content to render inside the section
    - className: additional CSS classes
    - gridCols: grid column configuration (default: 'grid-cols-1')
*/
const DashboardSection = ({ 
  title, 
  children, 
  className = '', 
  gridCols = 'grid-cols-1' 
}) => (
  <div className={`space-y-4 ${className}`}>
    {title && (
      <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
    )}
    <div className={`grid ${gridCols} gap-4`}>
      {children}
    </div>
  </div>
);

export default DashboardSection;

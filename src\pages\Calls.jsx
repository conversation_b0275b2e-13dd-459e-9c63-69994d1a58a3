import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import { activitiesService, formatActivitiesForTable, getActivityStatusColor } from "../services/activitiesService";
import { toast } from 'react-toastify';

const Calls = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [calls, setCalls] = useState([]);
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [error, setError] = useState(null);

  // Define columns of the table here and what to render
  const columns = [
    
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "anchor",
      title: "ANCHOR",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "mobile",
      title: "MOBILE",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },

     {
      key: "madeBy",
      title: "MADE BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActivityStatusColor(value, 'call')}`}>
          {value || 'Unknown'}
        </span>
      ),
    },
   
    {
      key: "date",
      title: "DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
   
  ];

  // Fetch calls data from API
  const fetchCalls = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching calls data...");

      const response = await activitiesService.getCalls();
      const formattedCalls = formatActivitiesForTable(response, 'call');

      setCalls(formattedCalls);
      console.log("Calls data loaded successfully:", formattedCalls.length, "calls");
    } catch (error) {
      console.error("Error fetching calls:", error);
      setError(error.message);
      toast.error(error.message || "Failed to load calls data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchCalls();
  }, []);

  // Date change handlers for DataTable
  const handleFromDateChange = (date) => {
    setFromDate(date);
  };

  const handleToDateChange = (date) => {
    setToDate(date);
  };







  const handleView = (call) => {
    console.log("View call:", call);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = async () => {
    setLoadingMore(true);
    try {
      console.log("Loading more calls...");
      // For now, just refresh the data
      // In a real implementation, you might have pagination
      await fetchCalls();
    } catch (error) {
      console.error("Error loading more calls:", error);
      toast.error("Failed to load more calls");
    } finally {
      setLoadingMore(false);
    }
  };



  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={calls}
          searchPlaceholder="Search calls..."
          onView={handleView}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Calls"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Date range filter
          showDateRangeFilter={true}
          dateRangeField="date"
          fromDate={fromDate}
          toDate={toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
        />

      </div>

    </PrivateLayout>
  
  )
}

export default Calls

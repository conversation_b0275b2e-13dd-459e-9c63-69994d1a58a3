import instance from "../axios/instance";

export const authService = {
  login: async (credentials) => {
    try {
      const response = await instance.post("/auth/login", credentials);

      // Store tokens in localStorage
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);

      return response.data;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  },

  logout: () => {
    // Clear tokens from localStorage
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  },

  // Password reset functionality
  requestPasswordReset: async (email) => {
    try {
      const response = await instance.post("/auth/forgot-password", { email });
      return response.data;
    } catch (error) {
      console.error("Request password reset error:", error);
      throw error;
    }
  },

  verifyResetOTP: async (email, otp) => {
    try {
      const response = await instance.post("/auth/verify-reset-otp", {
        email,
        otp
      });
      return response.data;
    } catch (error) {
      console.error("Verify reset OTP error:", error);
      throw error;
    }
  },

  resetPassword: async (email, token, password) => {
    try {
      const response = await instance.post("/auth/reset-password", {
        email,
        token,
        password
      });
      return response.data;
    } catch (error) {
      console.error("Reset password error:", error);
      throw error;
    }
  },

  resendResetOTP: async (email) => {
    try {
      const response = await instance.post("/auth/resend-reset-otp", { email });
      return response.data;
    } catch (error) {
      console.error("Resend reset OTP error:", error);
      throw error;
    }
  },
};
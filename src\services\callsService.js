import instance from '../axios/instance.jsx';

// Calls API endpoints
const ENDPOINTS = {
  CALLS: '/calls',
  CALL_BY_ID: (id) => `/calls/${id}`,
};

// Calls Service
export const callsService = {
  // Get all calls
  getAll: async () => {
    try {
      console.log('=== FETCHING ALL CALLS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.CALLS}`);
      
      const response = await instance.get(ENDPOINTS.CALLS);
      
      console.log('Calls response:', response.data);
      console.log('==============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching calls:', error);
      throw error;
    }
  },

  // Create a new call
  create: async (callData) => {
    try {
      console.log('=== CREATING NEW CALL ===');
      console.log('Call data:', callData);
      console.log(`API Endpoint: POST ${ENDPOINTS.CALLS}`);
      
      const response = await instance.post(ENDPOINTS.CALLS, callData);
      
      console.log('Call creation response:', response.data);
      console.log('=============================');
      
      return response.data;
    } catch (error) {
      console.error('Error creating call:', error);
      
      // Handle different error types
      if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid call data provided.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to create calls.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while creating call. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to create call.');
      }
    }
  },

  // Update an existing call
  update: async (id, callData) => {
    try {
      console.log('=== UPDATING CALL ===');
      console.log(`Call ID: ${id}`);
      console.log('Updated data:', callData);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.CALL_BY_ID(id)}`);
      
      const response = await instance.patch(ENDPOINTS.CALL_BY_ID(id), callData);
      
      console.log('Call update response:', response.data);
      console.log('=====================');
      
      return response.data;
    } catch (error) {
      console.error('Error updating call:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Call not found. It may have been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this call.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid call data provided.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while updating call. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update call.');
      }
    }
  },

  // Delete a call
  delete: async (id) => {
    try {
      console.log('=== DELETING CALL ===');
      console.log(`Call ID: ${id}`);
      console.log(`API Endpoint: DELETE ${ENDPOINTS.CALL_BY_ID(id)}`);
      
      const response = await instance.delete(ENDPOINTS.CALL_BY_ID(id));
      
      console.log('Call deletion response:', response.status);
      console.log('=====================');
      
      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting call:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Call not found. It may have already been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this call.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while deleting call. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to delete call.');
      }
    }
  },

  // Get a single call by ID
  getById: async (id) => {
    try {
      console.log('=== FETCHING CALL BY ID ===');
      console.log(`Call ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.CALL_BY_ID(id)}`);
      
      const response = await instance.get(ENDPOINTS.CALL_BY_ID(id));
      
      console.log('Call details response:', response.data);
      console.log('===========================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching call by ID:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Call not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view this call.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching call. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch call details.');
      }
    }
  },
};

/**
 * Format calls data for table display
 * @param {Object} apiResponse - Response from calls API
 * @returns {Array} - Formatted data for DataTable
 */
export const formatCallsForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((call, index) => ({
    id: call.id || `call${index + 1}`,
    name: call.lead_name || call.customer_name || "Unknown Lead",
    anchor: call.performed_by?.name || call.anchor_name || "Unknown",
    mobile: call.lead_phone || call.phone_number || "No phone",
    madeBy: call.performed_by?.name || call.created_by || "Unknown",
    status: call.call_status || call.status || "Unknown",
    date: formatCallDate(call.created_at || call.call_date),
    // Additional fields for detailed view
    duration: call.call_duration_minutes || call.duration,
    notes: call.notes || call.description,
    purpose: call.purpose?.name || call.call_purpose,
    nextFollowup: call.next_followup_date,
    leadId: call.lead_id,
    performedBy: call.performed_by,
    createdAt: call.created_at,
  }));
};

/**
 * Format call date for display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
const formatCallDate = (dateString) => {
  if (!dateString) return "Unknown date";
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (error) {
    console.warn('Error formatting call date:', dateString);
    return "Invalid date";
  }
};

export default callsService;

import { useState } from "react";

const DeleteConfirmation = ({
  item,
  onClose,
  onConfirm,
  itemName = "item",
  confirmText = "Delete",
  confirmButtonText = "Yes, Delete!",
  title = "Are you sure?",
  message,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      if (onConfirm) {
        onConfirm(item);
      }

      console.log("Deleted item:", item);
      onClose();
    } catch (error) {
      console.error("Error deleting item:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="text-center py-6 ">
      {/* Warning Icon - Orange circle with exclamation */}
      <div className="mx-auto flex items-center justify-center size-[5rem] rounded-full bg-orange-100 dark:bg-orange-900/20 mb-6">
        <div className="flex items-center justify-center size-[5rem] rounded-full border-2 border-orange-500 dark:border-orange-400">
          <span className="text-orange-500 dark:text-orange-400 text-[2rem] font-bold">
            !
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="mb-8">
        <h3 className="text-[22px] md:text-[25px] font-medium text-gray-900 dark:text-white mb-3">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-[14px] md:text-[16px]">
          {message || (
            <>
              You are about to {confirmText.toLowerCase()}{" "}
              {item?.name ? item.name.toUpperCase() : itemName.toUpperCase()}!
            </>
          )}
        </p>
      </div>

      {/* Actions */}
      <div className="flex justify-center space-x-4">
        <button
          type="button"
          onClick={onClose}
          disabled={isDeleting}
          className="px-6 py-3.5 text-sm font-medium text-gray-700 dark:text-gray-300
           bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded-md transition-colors duration-200 disabled:opacity-50"
        >
          No, cancel
        </button>
        <button
          type="button"
          onClick={handleDelete}
          disabled={isDeleting}
          className="px-6 py-2.5 text-sm font-medium text-white rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          style={{ backgroundColor: "#f46b68" }}
          onMouseEnter={(e) => {
            if (!isDeleting) {
              e.target.style.backgroundColor = "#e55a57";
            }
          }}
          onMouseLeave={(e) => {
            if (!isDeleting) {
              e.target.style.backgroundColor = "#f46b68";
            }
          }}
        >
          {isDeleting ? `Deleting...` : confirmButtonText}
        </button>
      </div>
    </div>
  );
};

export default DeleteConfirmation;

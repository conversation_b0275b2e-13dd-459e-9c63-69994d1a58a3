import { useState, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import AnchorForm from "../components/forms/AnchorForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import { anchorsService, formatAnchorsForTable, getAnchorUsageColor, formatAnchorDate } from "../services/anchorsService";
import { usePagination } from "../hooks/usePagination";
import { toast } from "react-toastify";

const Anchors = () => {
  // Pagination hook with backend integration
  const {
    data: anchors,
    loading,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    goToPage,
    changePageSize,
    refresh,
  } = usePagination({
    fetchData: useCallback(async (params) => {
      console.log('Fetching anchors with params:', params);
      const response = await anchorsService.getAll(params);
      // Format the response for table display
      const formattedData = formatAnchorsForTable(response);
      return {
        data: formattedData,
        meta: response.meta || {
          total: formattedData.length,
          page: params.page || 1,
          limit: params.limit || 10,
          totalPages: Math.ceil(formattedData.length / (params.limit || 10)),
          hasNextPage: false,
          hasPreviousPage: false,
        }
      };
    }, []),
    initialPageSize: 10,
    autoFetch: true,
  });

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "accountId",
      title: "ACCOUNT ID",
      render: (value) => (
        <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
          {value || "N/A"}
        </span>
      ),
    },
    {
      key: "email",
      title: "EMAIL",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "phoneNumber",
      title: "PHONE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400 font-mono">
          {value}
        </span>
      ),
    },
    {
      key: "leadsCount",
      title: "LEADS COUNT",
      render: (value, row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getAnchorUsageColor(row.isInUse, value)}`}>
          {value} leads
        </span>
      ),
    },
    {
      key: "isInUse",
      title: "STATUS",
      render: (value, row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value
            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
            : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
        }`}>
          {value ? "Active" : "Inactive"}
        </span>
      ),
    },
    {
      key: "createdAt",
      title: "CREATED AT",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatAnchorDate(value)}
        </span>
      ),
    },
  ];

  // Helper function to trigger refresh
  const refreshData = () => {
    refresh();
  };

  // Form submission handlers
  const handleCreateSubmit = async (formData) => {
    try {
      await anchorsService.create(formData);
      console.log("Created anchor:", formData);
      toast.success("Anchor created successfully!");
      // Refresh the data after successful creation
      refreshData();
    } catch (error) {
      console.error("Error creating anchor:", error);
      toast.error(error.message || "Failed to create anchor");
    }
  };

  const handleEditSubmit = async (formData, originalItem) => {
    try {
      await anchorsService.update(originalItem.id, formData);
      console.log("Updated anchor:", formData);
      toast.success("Anchor updated successfully!");
      // Refresh the data after successful update
      refreshData();
    } catch (error) {
      console.error("Error updating anchor:", error);
      toast.error(error.message || "Failed to update anchor");
    }
  };

  const handleDeleteConfirm = async (anchor) => {
    try {
      await anchorsService.delete(anchor.id);
      console.log("Deleted anchor:", anchor);
      toast.success("Anchor deleted successfully!");
      // Refresh the data after successful deletion
      refreshData();
    } catch (error) {
      console.error("Error deleting anchor:", error);
      toast.error(error.message || "Failed to delete anchor");
    }
  };

  const handleView = (anchor) => {
    console.log("View anchor:", anchor);
    // Here you would typically navigate to view page or show view modal
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    goToPage(page);
  };

  const handlePageSizeChange = (newPageSize) => {
    changePageSize(newPageSize);
  };

  // Import/Export handlers
  const handleExport = () => {
    console.log("Exporting anchors...");
    // Here you would typically generate and download an Excel file
  };

  const handleImport = (file) => {
    console.log("Importing anchors from file:", file.name);
    // Here you would typically process the uploaded file
    // Parse the data and make API calls to import anchors
  };









  return (
    <PrivateLayout>
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={anchors}
          searchPlaceholder="Search anchors..."
          addButtonText="New Anchor"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          // Pagination props
          showPagination={true}
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          pageSizeOptions={[10, 20, 50, 100]}
          showPageSizeSelector={true}
          showItemsInfo={true}
          showFirstLast={true}
          maxVisiblePages={5}
          highlightField="isInUse"
          highlightColors={{
            true: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            false: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <AnchorForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <AnchorForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Anchor"
            />
          )}
          createModalTitle="Create New Anchor"
          editModalTitle="Edit Anchor"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
          // Import/Export functionality
          showImportExport={true}
          onExport={handleExport}
          onImport={handleImport}
          importModalTitle="Import Anchors"
          importTemplateFileName="Anchors-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
        />
      </div>
    </PrivateLayout>
  );
};

export default Anchors;

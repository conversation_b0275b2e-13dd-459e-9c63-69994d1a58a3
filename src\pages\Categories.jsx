import { useState } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import CategoryForm from "../components/forms/CategoryForm";


const Categories = () => {
  const [loading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  // Define columns of the table here and what to render
  const columns = [
    
    {
      key: "name",
      title: "Category",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
   
    {
      key: "addedOn",
      title: "ADDED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
   
  ];

  // Data to be rendered in the table, should match the columns above
  const sampleCategories = [
    {
      id: "********",
      name: "INSTITUTION BANKING",
      addedBy: "RENOIR",
      addedOn: "July 16 2025",
    },
     {
      id: "********",
      name: "BUSINESS BANKING",
      addedBy: "JUNIOR",
      addedOn: "July 16 2025",
    },
    {
      id: "********",
      name: "RETAIL BANKING",
      addedBy: "MARCEL",
      addedOn: "July 16 2025",
    },
    {
      id: "********",
      name: "MICRO BANKING",
      addedBy: "TALIA",
      addedOn: "July 16 2025",
    },
    {
      id: "********",
      name: "LOAN BANKING",
      addedBy: "RENOIR",
      addedOn: "July 16 2025",
    },
    
  ];

   // Form submission handlers
  const handleCreateSubmit = (formData) => {
    console.log("Creating Category:", formData);
    // Here you would typically make an API call to create the customer category
    // After successful creation, you might want to refresh the data
  };

  const handleEditSubmit = (formData) => {
    console.log("Updating category:", formData);
    // Here you would typically make an API call to update the customer category
    // After successful update, you might want to refresh the data
  };

  const handleDeleteConfirm = (category) => {
    console.log("Deleting customer category:", category);
    // Here you would typically make an API call to delete the customer category
    // After successful deletion, you might want to refresh the data
  };

  const handleView = (category) => {
    console.log("View category:", category);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more customer categories");
    }, 2000);
  };


  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={sampleCategories}
          searchPlaceholder="Search ..."
          addButtonText="New Category"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Categories"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <CategoryForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <CategoryForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Category"
            />
          )}
          createModalTitle="Create New Category"
          editModalTitle="Category"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />

      </div>

    </PrivateLayout>
  
  )
}

export default Categories

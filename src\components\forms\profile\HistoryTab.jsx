import { useState, useEffect } from "react";
import { Phone, Calendar, Footprints, Loader2 } from "lucide-react";
import instance from "../../../axios/instance";

const HistoryTab = ({ onClose, item }) => {
  // State for interaction history
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch interaction history from API
  const fetchInteractionHistory = async () => {
    if (!item?.id) {
      setError("No lead ID provided");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log(`Fetching interaction history for lead ID: ${item.id}`);

      const response = await instance.get(
        `/activities/leads/${item.id}/interaction-history`
      );
      console.log("Interaction history response:", response.data);

      // Transform API data to match UI format
      const transformedData = response.data.map((interaction, index) => ({
        id: index + 1,
        officer: interaction.performed_by,
        date: formatDate(interaction.created_at),
        type: interaction.interaction_type, // "visit" or "call"
        status: interaction.notes,
        outcome: interaction.activity_type, // "First Visit", "First Contact", etc.
        nextFollowUp: interaction.next_followup_date
          ? formatDate(interaction.next_followup_date)
          : "Not set",
        color: getInteractionColor(interaction.activity_type),
        rawData: interaction, // Keep original data for reference
      }));

      setHistoryData(transformedData);
    } catch (error) {
      console.error("Error fetching interaction history:", error);
      setError(
        error.response?.data?.message || "Failed to load interaction history"
      );
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Not set";

    const date = new Date(dateString);
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    };

    return date.toLocaleDateString("en-GB", options);
  };

  // Get color based on activity type
  const getInteractionColor = (activityType) => {
    // Green for successful interactions, red for follow-ups or unsuccessful ones
    const successfulTypes = ["First Contact", "First Visit", "Successful"];
    return successfulTypes.some((type) => activityType?.includes(type))
      ? "green"
      : "red";
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchInteractionHistory();
  }, [item?.id]);

  const getStatusIcon = (type) => {
    if (type === "call") return Phone;
    if (type === "visit") return Footprints;
    return Calendar;
  };

  const getIconColor = (type) => {
    if (type === "call") return "#f97316"; // orange-500
    if (type === "visit") return "#8b5cf6"; // violet-500
    return "#6b7280"; // gray-500
  };

  const getOutcomeColor = (outcome) => {
    return outcome === "First Contact" ? "#369dc9" : "#1c5b41";
  };

  return (
    <div className="px-6 -mx-6">
      <div className="pl-[7%] pr-[10%] py-6">
        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">
              Loading interaction history...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-red-500 mb-2">⚠️ Error</div>
              <div className="text-gray-600">{error}</div>
              <button
                onClick={fetchInteractionHistory}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && historyData.length === 0 && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-gray-400 mb-2">📋</div>
              <div className="text-gray-600">No interaction history found</div>
              <div className="text-sm text-gray-500 mt-1">
                Interactions will appear here once activities are recorded
              </div>
            </div>
          </div>
        )}

        {/* Timeline */}
        {!loading && !error && historyData.length > 0 && (
          <div className="relative">
            {historyData.map((item, index) => {
              const StatusIcon = getStatusIcon(item.type);
              const isLast = index === historyData.length - 1;

              return (
                <div key={item.id} className="relative flex items-start mb-8">
                  {/* Timeline line */}
                  {!isLast && (
                    <div
                      className="absolute left-[11px] top-[24px] w-[2px] h-[calc(100%+16px)]"
                      style={{
                        backgroundColor:
                          item.color === "red" ? "#ff6b6b" : "#4ade80",
                      }}
                    />
                  )}

                  {/* Timeline dot */}
                  <div
                    className="flex-shrink-0 w-[6px] h-[6px] rounded-full mt-[9px] mr-4"
                    style={{
                      backgroundColor:
                        item.color === "red" ? "#ff6b6b" : "#4ade80",
                    }}
                  />

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm text-gray-400">
                        {item.officer} | {item.date}
                      </span>
                    </div>

                    {/* Status with Icon */}
                    <div className="flex items-center gap-3 mb-2">
                      <div
                        className="p-2 rounded"
                        style={{ backgroundColor: getIconColor(item.type) }}
                      >
                        <StatusIcon size={20} className="text-white" />
                      </div>
                      <span className="font-medium text-gray-800">
                        {item.status}
                      </span>
                    </div>

                    {/* Outcome Badge */}
                    <div className="mb-2">
                      <span
                        className="px-2 py-1 text-xs font-medium text-white rounded"
                        style={{
                          backgroundColor: getOutcomeColor(item.outcome),
                        }}
                      >
                        {item.outcome}
                      </span>
                    </div>

                    {/* Next Follow up */}
                    <div className="text-sm text-gray-500">
                      Next Follow up date 📅 {item.nextFollowUp}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Close Button */}
      <div className="flex justify-end pt-8 pb-6 pr-[10%]">
        <button
          onClick={onClose}
          className="px-7 py-3 text-sm font-medium rounded-lg transition-colors"
          style={{
            color: "#f46b68",
            backgroundColor: "#fef7f7",
          }}
          onMouseEnter={(e) => {
            e.target.style.backgroundColor = "#f46b68";
            e.target.style.color = "white";
          }}
          onMouseLeave={(e) => {
            e.target.style.backgroundColor = "#fef7f7";
            e.target.style.color = "#f46b68";
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default HistoryTab;

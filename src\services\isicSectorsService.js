import instance from '../axios/instance.jsx';

// ISIC Sectors API endpoints
const ENDPOINTS = {
  ISIC_SECTORS: '/isic-sectors',
  ISIC_SECTOR_BY_ID: (id) => `/isic-sectors/${id}`,
};

// ISIC Sectors Service
export const isicSectorsService = {
  // Get all ISIC sectors
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.ISIC_SECTORS);
      return response.data;
    } catch (error) {
      console.error('Error fetching ISIC sectors:', error);
      throw error;
    }
  },

  // Create a new ISIC sector
  create: async (sectorData) => {
    try {
      const response = await instance.post(ENDPOINTS.ISIC_SECTORS, sectorData);
      return response.data;
    } catch (error) {
      console.error('Error creating ISIC sector:', error);
      throw error;
    }
  },

  // Update an existing ISIC sector
  update: async (id, sectorData) => {
    try {
      const response = await instance.patch(ENDPOINTS.ISIC_SECTOR_BY_ID(id), sectorData);
      return response.data;
    } catch (error) {
      console.error('Error updating ISIC sector:', error);
      throw error;
    }
  },

  // Delete an ISIC sector
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.ISIC_SECTOR_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting ISIC sector:', error);
      throw error;
    }
  },

  // Get a single ISIC sector by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.ISIC_SECTOR_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching ISIC sector by ID:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatSectorData = (sector) => {
  return {
    id: sector.id,
    code: sector.code || 'N/A',
    name: sector.name,
    addedOnDate: sector.addedOnDate,
    addedBy: sector.addedBy || 'N/A',
    // Format date for display
    formattedDate: formatDateTime(sector.addedOnDate),
  };
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatSectorsResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatSectorData);
};

export default isicSectorsService;

@import "tailwindcss";

/* Custom Scrollbars */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(243, 244, 246, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.4);
    border-radius: 3px;
    border: none;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.6);
}

/* Dark mode scrollbars */
.dark ::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
}

.dark ::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.6);
    border: 1px solid rgba(31, 41, 55, 0.8);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.8);
}

/* Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.4) rgba(243, 244, 246, 0.3);
}

.dark * {
    scrollbar-color: rgba(75, 85, 99, 0.4) rgba(31, 41, 55, 0.3);
}

@layer base {

    *,
    *::before,
    *::after {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
    }

    /* Scrollbars are handled by custom styles above */
}

/* React Select custom styles */
.react-select-container .react-select__control {
    border-color: #d1d5db !important;
    box-shadow: none !important;
    min-height: 38px;
}

.react-select-container .react-select__control:hover {
    border-color: #9ca3af !important;
}

.react-select-container .react-select__control--is-focused {
    border-color: #9ca3af !important;
    box-shadow: none !important;
}

.react-select-container .react-select__value-container {
    padding: 2px 8px;
}

.react-select-container .react-select__input-container {
    margin: 0;
    padding: 0;
}

.react-select-container .react-select__placeholder {
    color: #9ca3af;
    font-size: 14px;
}

.react-select-container .react-select__single-value {
    color: #374151;
    font-size: 14px;
}

.react-select-container .react-select__multi-value {
    background-color: #f3f4f6;
    border-radius: 4px;
}

.react-select-container .react-select__multi-value__label {
    color: #374151;
    font-size: 13px;
}

.react-select-container .react-select__multi-value__remove {
    color: #6b7280;
}

.react-select-container .react-select__multi-value__remove:hover {
    background-color: #ef4444;
    color: white;
}

.react-select-container .react-select__menu {
    border: 1px solid #d1d5db;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-select-container .react-select__option {
    font-size: 14px;
}

.react-select-container .react-select__option--is-focused {
    background-color: #f3f4f6;
}

.react-select-container .react-select__option--is-selected {
    background-color: #165026;
}

/* Custom Toast Styles */
.success-toast {
    border-left-color: #27AE60 !important;
    color: #27AE60 !important;
}

.error-toast {
    border-left-color: #E74C3C !important;
    color: #E74C3C !important;
}

.warning-toast {
    border-left-color: #F39C12 !important;
    color: #F39C12 !important;
}

.default-toast {
    border-left-color: #3498DB !important;
    color: #3498DB !important;
}
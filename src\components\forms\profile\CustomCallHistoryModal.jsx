import { useState, useEffect } from "react";
import {
  Phone,
  Loader2,
  MessageSquare,
  PhoneCall,
  MessageSquareText,
} from "lucide-react";
import instance from "../../../axios/instance";

const CustomCallHistoryModal = ({ item, onClose }) => {
  const [callHistory, setCallHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCallHistory = async () => {
      if (!item?.id) {
        setError("No record ID provided");
        setLoading(false);
        return;
      }
      try {
        setLoading(true);
        setError(null);
        const response = await instance.get(
          `/customer-service/hitlist-record/${item.id}/call-history`
        );
        // Only show call interactions (should be all, but filter just in case)
        const filtered = (response.data || []).filter(
          (entry) => entry.interaction_type === "call"
        );
        setCallHistory(filtered);
      } catch (err) {
        setError(err.response?.data?.message || "Failed to load call history");
      } finally {
        setLoading(false);
      }
    };
    fetchCallHistory();
  }, [item]);

  const formatDate = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    } catch {
      return "";
    }
  };

  const formatTime = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "";
    }
  };

  return (
    <div className="h-[80vh] flex flex-col">
      <div className="flex-1 overflow-y-auto pr-2 custom-scrollbar-thin">
        <div className="px-2 py-6">
          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">
                Loading call history...
              </span>
            </div>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="text-red-500 mb-2">⚠️ Error</div>
                <div className="text-gray-600">{error}</div>
                <button
                  onClick={onClose}
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && callHistory.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <Phone className="w-8 h-8 text-gray-400" strokeWidth={1.5} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">
                No Call History
              </h3>
              <p className="text-gray-600">
                This client doesn't have any recorded calls yet.
              </p>
              <div className="bg-gray-50 w-full max-w-md p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-5 h-5 text-gray-400" />
                  <p className="text-gray-600">
                    All future calls will appear here automatically
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Call History */}
          {!loading && !error && callHistory.length > 0 && (
            <div className="space-y-6">
              {callHistory.map((call, index) => (
                <div
                  key={index}
                  className="bg-white border border-[rgba(28,91,65,0.3)] rounded-lg shadow p-6 space-y-4"
                >
                  {/* Status Badge */}
                  <div className="flex justify-between items-center space-x-2">
                    <div className="flex items-center gap-2">
                      <div className="bg-[#1c5b41] text-white rounded-full w-fit p-3">
                        <PhoneCall size={20} />
                      </div>

                      <div
                        className={`${
                          call.call_status.toLowerCase() == "success"
                            ? "text-green-600"
                            : "text-red-600"
                        }  px-3 py-1 h-fit rounded-full text-sm font-medium`}
                      >
                        {call.call_status}
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <p className=" text-gray-500 font-semibold">
                        {formatDate(call.performed_at)}
                      </p>
                      <span className="text-gray-500">
                        {formatTime(call.performed_at)}
                      </span>
                    </div>
                  </div>

                  {/* Client Info */}
                  <div className="grid grid-cols-3 gap-6">
                    <div>
                      <p className="text-sm text-gray-500">Performed by</p>
                      <p className="text-base font-medium text-gray-900">
                        {call.performed_by?.name}{" "}
                        <span className="text-gray-400 font-[400] text-sm ml-2">
                          ({call.performed_by?.rm_code})
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Customer feedback</p>
                      <p className="text-base font-medium text-gray-900">
                        {call.customer_feedback}
                      </p>
                    </div>
                    {/* <div>
                      <p className="text-sm text-gray-500">Purpose</p>
                      <p className="text-base font-medium text-gray-900">
                        {call.purpose || "N/A"}
                      </p>
                    </div> */}
                  </div>

                  {/* Notes */}
                  {call.notes && (
                    <div>
                      <div className="flex items-center gap-[0.5rem] border-t border-t-[rgba(101,115,129,0.2)] pt-[0.5rem]">
                        <MessageSquareText size={24} />
                        <h4 className="text-[16px] text-gray-800 mb-1 font-semibold">
                          Notes
                        </h4>
                      </div>
                      <p className="bg-[#f1f5f9] text-[#657381] p-4 mt-3 rounded-[0.4rem]">
                        {call.notes}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomCallHistoryModal;

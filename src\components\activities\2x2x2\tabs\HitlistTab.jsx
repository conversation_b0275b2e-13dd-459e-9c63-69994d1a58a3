import DataTable from "../../../common/DataTable";
import { hitlistColumns } from "../config/columns.jsx";
import { allActivities } from "../data/sampleData";
import { toast } from "react-toastify";
import { customerServiceService } from "../../../../services/customerServiceService";

const HitlistTab = ({
  loading,
  loadingMore,
  onView,
  onLoadMore,
  customHeaderContent,
}) => {
  const [hitlists, setHitlists] = useState(allActivities.hitlist);
  // Handle import, export, and print actions
  const handleImport = async (importResult) => {
    try {
      // Add the new hitlist to state with 0 completion
      const newHitlist = {
        id: importResult.hitlistId,
        code: importResult.hitlistCode,
        uploadedBy: importResult.uploadedBy,
        uploadDate: importResult.uploadDate,
        type: importResult.type,
        numberOfRecords: importResult.numberOfRecords,
        completion: 0
      };

      setHitlists(prev => [newHitlist, ...prev]);
      toast.success(`Successfully imported hitlist ${importResult.hitlistCode}`);
    } catch (error) {
      console.error("Error handling import result:", error);
      toast.error("Failed to update hitlist display");
      throw error;
    }
  };

  const handleDownloadTemplate = () => {
    window.open(customerServiceService.getHitlistTemplateUrl(), '_blank');
  };

  const handleExport = () => {
    console.log("Export hitlist data");
    // TODO: Implement export functionality
  };

  const handlePrint = () => {
    console.log("Print hitlist data");
    // TODO: Implement print functionality
  };

  const handleCreate = () => {
    console.log("Create new hitlist entry");
    // TODO: Implement create functionality
  };

  return (
    <DataTable
      key="hitlist-table"
      columns={hitlistColumns}
      data={hitlists}
      searchPlaceholder="Search hitlist..."
      addButtonText="New Hitlist Entry"
      onView={onView}
      onAdd={handleCreate}
      actions={["edit", "delete"]}
      loading={loading}
      loadingMore={loadingMore}
      loadMoreText="Load More Hitlist Entries"
      onLoadMore={onLoadMore}
      showLoadMore={true}
      dataCountLabel="hitlist"
      customHeaderContent={customHeaderContent}
      createModalTitle="Create New Hitlist Entry"
      editModalTitle="Edit Hitlist Entry"
      deleteModalTitle="Delete Hitlist Entry"
      modalSize="lg"
      // Enable import, export, and print
      showImportExport={true}
      onImport={handleImport}
      onExport={handleExport}
      onPrint={handlePrint}
      onDownloadTemplate={handleDownloadTemplate}
      importModalTitle="Import Hitlist"
      importTemplateFileName="Hitlist-Template.xlsx"
      importAcceptedFileTypes=".xlsx,.xls,.csv"
      showHitlistTypeSelection={true}
      isHitlistImport={true}
    />
  );
};

export default HitlistTab;

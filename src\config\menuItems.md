# Centralized Menu Configuration

This file contains the centralized menu configuration for both desktop and mobile sidebars, ensuring consistency across the application.

## Structure

### Menu Item Object
```javascript
{
  id: "unique-identifier",
  label: "Display Name",
  icon: IconComponent,
  href: "/route-path",
  hasChildren: true, // optional
  children: [ // optional
    {
      id: "child-id",
      label: "Child Display Name",
      href: "/child-route-path"
    }
  ]
}
```

### Properties

- **id**: Unique identifier for the menu item
- **label**: Display text shown in the sidebar
- **icon**: React icon component (from react-icons or lucide-react)
- **href**: Route path for navigation (use "#" for placeholder/disabled items)
- **hasChildren**: <PERSON>olean indicating if the item has sub-menu items
- **children**: Array of child menu items (only for parent items)

## Usage

### In Components
```javascript
import { menuItems, getPageTitleFromRoute } from "../config/menuItems";

// Use menu items in sidebar components
const sidebarItems = menuItems;

// Get page title from current route
const pageTitle = getPageTitleFromRoute(location.pathname);
```

### Adding New Routes

1. **Add to menuItems array**:
```javascript
{
  id: "new-page",
  label: "New Page",
  icon: MdNewIcon,
  href: "/new-page",
}
```

2. **Add route to App.jsx**:
```javascript
<Route
  path="/new-page"
  element={
    <ProtectedRoute>
      <NewPage />
    </ProtectedRoute>
  }
/>
```

3. **The page title will automatically be resolved** by `getPageTitleFromRoute()`

### Adding Sub-Menu Items

```javascript
{
  id: "parent-item",
  label: "Parent Item",
  icon: MdParentIcon,
  href: "#", // Parent doesn't need direct navigation
  hasChildren: true,
  children: [
    { id: "child-1", label: "Child 1", href: "/parent/child-1" },
    { id: "child-2", label: "Child 2", href: "/parent/child-2" },
  ],
}
```

## Current Menu Structure

- **Dashboard** (`/dashboard`)
- **Items** (Parent with children)
  - Products, Categories, Departments, etc.
- **Track** (Parent with children)
  - Track Items, Track History
- **Leads** (Parent with children)
  - All Leads, New Leads, Qualified Leads
- **Anchors** (`/anchors`)
- **Customers** (Placeholder)
- **Administration** (Parent with children)
  - Users, Roles, Permissions
- **Support Tickets** (Placeholder)

## Helper Functions

### getPageTitleFromRoute(pathname)
Automatically determines the page title based on the current route:
- Checks direct menu item matches first
- Falls back to checking child menu items
- Returns "Dashboard" as default fallback

## Benefits

1. **Single Source of Truth**: All menu configuration in one place
2. **Consistency**: Desktop and mobile sidebars use the same data
3. **Maintainability**: Easy to add/remove/modify menu items
4. **Automatic Title Resolution**: Page titles are automatically determined
5. **Type Safety**: Centralized imports reduce import errors
6. **Scalability**: Easy to extend with new features (permissions, badges, etc.)

## Best Practices

1. **Use meaningful IDs**: Make IDs descriptive and unique
2. **Consistent Naming**: Use kebab-case for IDs and routes
3. **Icon Consistency**: Use icons from the same icon library
4. **Route Planning**: Plan your route structure before adding menu items
5. **Placeholder Routes**: Use "#" for items not yet implemented
6. **Hierarchical Structure**: Group related items under parent menus

import React from 'react';

/*
  SummaryCard Component: displays a KPI with a title, value, and icon.
  Props:
    - title: label of the metric
    - value: numeric value to display
    - icon: SVG icon component
    - iconColor: Tailwind text color for the icon
    - bgColor: Tailwind background color for the card
*/
const SummaryCard = ({ title, value, icon: Icon, iconColor, bgColor }) => (
  <div className={`flex items-center p-4 rounded-lg shadow ${bgColor} min-h-[7rem]`}>
    <div className={`p-2 rounded-full ${iconColor} bg-grey bg-opacity-30`}>
      <Icon className="w-6 h-6" /> {/* slightly increased icon size for balance */}
    </div>
    <div className="ml-4">
      <p className="text-sm font-medium text-gray-600">{title}</p>
      <p className="mt-1 text-xl font-semibold text-gray-900">{value}</p>
    </div>
  </div>
);

export default SummaryCard;

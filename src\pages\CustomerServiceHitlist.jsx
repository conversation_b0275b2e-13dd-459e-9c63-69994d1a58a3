import { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Eye } from "lucide-react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import { customerServiceService } from "../services/customerServiceService";
import { formatTime } from "../utils/dateUtils";

const CustomerServiceHitlist = () => {
  const { type } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [hitlists, setHitlists] = useState([]);

  // Filter state
  const [filters, setFilters] = useState({
    type: "",
  });

  // Filter configuration
  const filterConfig = [
    {
      key: "type",
      label: "Type",
      field: "type",
      placeholder: "All Types",
      selectedValue: filters.type,
      options: [
        { value: "2by2by2", label: "2by2by2" },
        { value: "Dormancy", label: "Dormancy" },
      ],
    },
  ];

  // Handle filter changes
  const handleFilterChange = (filterKey, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterKey]: value,
    }));
  };

  // Handle clearing filters
  const handleClearFilters = () => {
    setFilters({
      type: "",
    });
  };

  // Fetch hitlists
  useEffect(() => {
    const fetchHitlists = async () => {
      try {
        setLoading(true);
        const data = await customerServiceService.getHitlists();
        setHitlists(data);
      } catch (error) {
        console.error("Error fetching hitlists:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchHitlists();
  }, []);

  // Apply filters to data
  const filteredHitlistData = hitlists.filter((hitlist) => {
    // Type filter
    if (filters.type && hitlist.type !== filters.type) {
      return false;
    }
    return true;
  });

  // Format date function
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Define columns for hitlist table
  const columns = [
    {
      key: "hitlistCode",
      title: "HITLIST CODE",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "type",
      title: "TYPE",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "uploadedBy",
      title: "UPLOADED BY",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "uploadDate",
      title: "UPLOAD DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(value)} <strong className="mr-2"></strong>{" "}
          {formatTime(value)}
        </span>
      ),
    },
    {
      key: "numberOfRecords",
      title: "# OF RECORDS",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "completion",
      title: "COMPLETION",
      render: (value) => {
        const percentage = parseInt(value);
        return (
          <div className="flex items-center space-x-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2 dark:bg-gray-700 min-w-[80px]">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${percentage}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[40px]">
              {value}
            </span>
          </div>
        );
      },
    },
    {
      key: "actions",
      title: "ACTIONS",
      render: (value, row) => (
        <button
          onClick={() => handleView(row)}
          className="p-1 hover:bg-gray-100 rounded transition-colors"
          title="View Details"
        >
          <Eye size={16} className="text-gray-500 hover:text-gray-700" />
        </button>
      ),
    },
  ];

  // Event handlers
  const handleView = (item) => {
    setSelectedItem(item);
    // Navigate to hitlist details page using hitlistCode
    navigate(`/customer-service/hitlist/${item.hitlistCode}`);
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more hitlist entries");
    }, 2000);
  };

  // Import/Export handlers
  const handleImport = async (importData) => {
    try {
      console.log("Importing hitlist data:", importData);
      const response = await customerServiceService.importHitlist(importData);

      // Update the hitlists state with the new data
      // Response.data contains the single hitlist object, not an array
      console.log("Response here we go:", response);

      setHitlists((prevHitlists) => [
        { ...response.data, completion: "0%" },
        ...prevHitlists,
      ]);

      return response;
    } catch (error) {
      console.error("Error importing hitlist:", error);
      throw error;
    }
  };

  const handleExport = () => {
    console.log("Exporting hitlist data");
    // API integration logic here
  };

  const handlePrint = () => {
    console.log("Printing hitlist data");
    // Print functionality here
  };

  const handleDownloadTemplate = () => {
    try {
      customerServiceService.downloadHitlistTemplate();
    } catch (error) {
      console.error("Error downloading template:", error);
    }
  };

  return (
    <PrivateLayout pageTitle="Customer Service - Hitlist">
      <div className="space-y-6">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={filteredHitlistData}
          searchPlaceholder="Search hitlists..."
          onView={handleView}
          actions={[]} // Hide default actions column
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Hitlists"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          dataCountLabel="hitlists"
          showDataCount={true}
          // Import/Export functionality
          showImportExport={true}
          onImport={handleImport}
          onExport={handleExport}
          onPrint={handlePrint}
          importModalTitle="Import Hitlists"
          importTemplateFileName="Hitlists-Template.xlsx"
          importAcceptedFileTypes=".xlsx,.xls,.csv"
          // Custom import props for hitlists
          showHitlistTypeSelection={true}
          isHitlistImport={true}
          onDownloadTemplate={handleDownloadTemplate}
          // Filter functionality
          showFilters={true}
          filterConfig={filterConfig}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />
      </div>
    </PrivateLayout>
  );
};

export default CustomerServiceHitlist;

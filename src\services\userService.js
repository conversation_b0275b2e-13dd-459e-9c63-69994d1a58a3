import instance from '../axios/instance.jsx';
import { toast } from 'react-toastify';

/**
 * User Service - Handles all user-related API operations
 * 
 * This service provides methods for:
 * - User CRUD operations (Create, Read, Update, Delete)
 * - Role and branch data fetching for form dropdowns
 * - Data formatting and validation
 * - Error handling with user-friendly messages
 */

// API endpoints for user operations
const ENDPOINTS = {
  USERS: '/users',
  USER_BY_ID: (id) => `/users/${id}`,
  ROLES: '/roles',
  BRANCHES: '/branches',
};

/**
 * Users Service - Main service object containing all user operations
 */
export const usersService = {
  /**
   * Fetch all users from the backend
   * @returns {Promise<Array>} Array of user objects
   */
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.USERS);
      console.log('Raw users response:', response.data);
      
      // Handle different response formats (with or without data wrapper)
      if (response.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn('Unexpected users response format:', response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  /**
   * Fetch a single user by ID
   * @param {string} id - User ID
   * @returns {Promise<Object>} User object
   */
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.USER_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      throw error;
    }
  },

  /**
   * Create a new user
   * @param {Object} userData - User data object
   * @returns {Promise<Object>} Created user object
   */
  create: async (userData) => {
    try {
      console.log('Creating user with data:', userData);
      
      // Format payload according to backend expectations
      const payload = formatUserDataForBackend(userData);
      console.log('Formatted payload for backend:', payload);
      console.log('Payload keys:', Object.keys(payload));
      console.log('Payload values:', Object.values(payload));
      
      const response = await instance.post(ENDPOINTS.USERS, payload);
      toast.success('User created successfully!');
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Show specific error message based on error type
      let errorMessage = 'Failed to create user';

      if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timed out. Please try again.';
      } else if (error.code === 'ERR_NETWORK') {
        errorMessage = 'Network error. Please check your connection.';
      } else if (error.response?.data?.message) {
        // Handle validation errors (message can be array or string)
        const message = error.response.data.message;
        if (Array.isArray(message)) {
          console.error('Validation errors:', message);
          errorMessage = `Validation errors: ${message.join(', ')}`;
        } else {
          errorMessage = message;
        }
      } else if (error.response?.status === 409) {
        errorMessage = 'User with this email already exists.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid user data. Please check all fields.';
      }

      toast.error(errorMessage);
      throw error;
    }
  },

  /**
   * Update an existing user
   * @param {string} id - User ID
   * @param {Object} userData - Updated user data
   * @returns {Promise<Object>} Updated user object
   */
  update: async (id, userData) => {
    try {
      console.log('Updating user with ID:', id, 'and data:', userData);
      
      // Format payload according to backend expectations
      const payload = formatUserDataForBackend(userData, true); // true indicates update operation
      console.log('Formatted payload for backend:', payload);
      
      const response = await instance.patch(ENDPOINTS.USER_BY_ID(id), payload);
      toast.success('User updated successfully!');
      return response.data;
    } catch (error) {
      console.error('Error updating user:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Show specific error message based on error type
      let errorMessage = 'Failed to update user';

      if (error.code === 'ECONNABORTED') {
        errorMessage = 'Request timed out. Please try again.';
      } else if (error.code === 'ERR_NETWORK') {
        errorMessage = 'Network error. Please check your connection.';
      } else if (error.response?.data?.message) {
        // Handle validation errors (message can be array or string)
        const message = error.response.data.message;
        if (Array.isArray(message)) {
          console.error('Validation errors:', message);
          errorMessage = `Validation errors: ${message.join(', ')}`;
        } else {
          errorMessage = message;
        }
      } else if (error.response?.status === 409) {
        errorMessage = 'User with this email already exists.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid user data. Please check all fields.';
      }

      toast.error(errorMessage);
      throw error;
    }
  },

  /**
   * Delete a user
   * @param {string} id - User ID
   * @returns {Promise<boolean>} Success status
   */
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.USER_BY_ID(id));
      toast.success('User deleted successfully!');
      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting user:', error);
      
      let errorMessage = 'Failed to delete user';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      toast.error(errorMessage);
      throw error;
    }
  },

  /**
   * Fetch all roles for dropdown options
   * @returns {Promise<Array>} Array of role objects
   */
  getRoles: async () => {
    try {
      const response = await instance.get(ENDPOINTS.ROLES);
      
      // Handle different response formats
      let roles = [];
      if (response.data && Array.isArray(response.data.data)) {
        roles = response.data.data;
      } else if (Array.isArray(response.data)) {
        roles = response.data;
      }
      
      // Format roles for react-select
      return roles.map(role => ({
        value: role.id,
        label: role.name,
        ...role
      }));
    } catch (error) {
      console.error('Error fetching roles:', error);
      // Return empty array on error to prevent form breaking
      return [];
    }
  },

  /**
   * Fetch all branches for dropdown options
   * @returns {Promise<Array>} Array of branch objects
   */
  getBranches: async () => {
    try {
      const response = await instance.get(ENDPOINTS.BRANCHES);
      
      // Handle different response formats
      let branches = [];
      if (response.data && Array.isArray(response.data.data)) {
        branches = response.data.data;
      } else if (Array.isArray(response.data)) {
        branches = response.data;
      }
      
      // Format branches for react-select
      return branches.map(branch => ({
        value: branch.id,
        label: branch.name,
        ...branch
      }));
    } catch (error) {
      console.error('Error fetching branches:', error);
      // Return empty array on error to prevent form breaking
      return [];
    }
  },
};

/**
 * Format user data from frontend form to backend expected format
 * @param {Object} formData - Form data from UserForm
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Formatted data for backend
 */
const formatUserDataForBackend = (formData, isUpdate = false) => {
  const payload = {
    name: formData.name?.trim(),
    email: formData.email?.trim(),
    phone_number: formData.phone?.trim(), // Backend expects phone_number
    role_id: formData.role, // Should be role ID from dropdown
    branch_id: formData.branch, // Should be branch ID from dropdown
    rm_code: formData.rm_code?.trim() || null, // Optional field
  };

  // Only include password for new users or when password is provided in updates
  // Note: confirmPassword is never sent to backend - it's only for frontend validation
  if (!isUpdate || (formData.password && formData.password.trim())) {
    payload.password = formData.password.trim();
  }

  // Remove undefined/null/empty values
  Object.keys(payload).forEach(key => {
    if (payload[key] === undefined || payload[key] === null || payload[key] === '') {
      delete payload[key];
    }
  });

  return payload;
};

/**
 * Format user data from backend to frontend form format
 * @param {Object} userData - User data from backend
 * @returns {Object} Formatted data for form
 */
export const formatUserDataForForm = (userData) => {
  return {
    name: userData.name || '',
    email: userData.email || '',
    phone: userData.phone_number || '', // Backend sends phone_number
    role: userData.role_id || '', // Backend sends role_id
    branch: userData.branch_id || '', // Backend sends branch_id
    rm_code: userData.rm_code || '',
    // Don't populate password fields for security
    password: '',
    confirmPassword: '',
  };
};

/**
 * Validate user form data
 * Note: confirmPassword is only used for frontend validation and is never sent to backend
 * @param {Object} formData - Form data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Object} Validation errors object
 */
export const validateUserData = (formData, isUpdate = false) => {
  const errors = {};

  // Name validation
  if (!formData.name?.trim()) {
    errors.name = 'Name is required';
  }

  // Email validation
  if (!formData.email?.trim()) {
    errors.email = 'Email is required';
  } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
    errors.email = 'Email is invalid';
  }

  // Phone validation - handle both field names for compatibility
  const phoneValue = formData.phone_number?.trim() || formData.phone?.trim();
  if (!phoneValue) {
    errors.phone_number = 'Phone number is required';
  }

  // Role validation
  if (!formData.role) {
    errors.role = 'Role is required';
  }

  // Branch validation
  if (!formData.branch) {
    errors.branch = 'Branch is required';
  }

  // Password validation (confirmPassword is only for frontend validation)
  if (!isUpdate) {
    // For new users, password is required
    if (!formData.password?.trim()) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }

    // Confirm password validation (frontend only - never sent to backend)
    if (!formData.confirmPassword?.trim()) {
      errors.confirmPassword = 'Please confirm password';
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
  } else {
    // For updates, validate password only if provided
    if (formData.password?.trim()) {
      if (formData.password.length < 8) {
        errors.password = 'Password must be at least 8 characters';
      }

      // Confirm password validation (frontend only - never sent to backend)
      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }
    }
  }

  return errors;
};

export default usersService;

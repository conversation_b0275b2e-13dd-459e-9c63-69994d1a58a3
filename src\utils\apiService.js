import axios from "axios";

// Get the base URL from environment variables
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://localhost:5173/api/v1";

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem("authToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem("authToken");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export default apiClient;

// Region API endpoints
export const regionEndpoints = {
  getAll: () => apiClient.get("/regions"),
  getById: (id) => apiClient.get(`/regions/${id}`),
  create: (data) => apiClient.post("/regions", data),
  update: (id, data) => apiClient.patch(`/regions/${id}`, data),
  delete: (id) => apiClient.delete(`/regions/${id}`),
};

// Branch API endpoints
export const branchEndpoints = {
  getAll: () => apiClient.get("/branches"),
  getById: (id) => apiClient.get(`/branches/${id}`),
  create: (data) => apiClient.post("/branches", data),
  update: (id, data) => apiClient.patch(`/branches/${id}`, data),
  delete: (id) => apiClient.delete(`/branches/${id}`),
};

// Export the base URL for use in other parts of the app
export { API_BASE_URL };

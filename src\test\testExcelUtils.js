// Test file for Excel utilities
import { downloadLeadsTemplate, parseLeadsFile } from '../utils/excelUtils.js';

// Test template generation
console.log('Testing Excel template generation...');
try {
  downloadLeadsTemplate();
  console.log('✅ Template generation successful');
} catch (error) {
  console.error('❌ Template generation failed:', error);
}

// Test CSV data
const csvData = `Customer Name *,Phone Number *,Customer Category,ISIC Sector,Lead Type (New/Existing),Client ID (for Existing leads),Branch Name,Contact Person Name,Contact Person Phone,Employer Name
<PERSON>,0712345678,Employed,Manufacturing,New,,Nairobi Main Branch,Jane Do<PERSON>,0712345679,ABC Company
Mary Smith,0723456789,Self Employed,Retail Trade,Existing,CL001234,Westlands Branch,,,`;

// Create a test CSV file
const csvBlob = new Blob([csvData], { type: 'text/csv' });
const csvFile = new File([csvBlob], 'test-leads.csv', { type: 'text/csv' });

// Test CSV parsing
console.log('Testing CSV parsing...');
parseLeadsFile(csvFile)
  .then(leads => {
    console.log('✅ CSV parsing successful:', leads);
  })
  .catch(error => {
    console.error('❌ CSV parsing failed:', error);
  });

export { downloadLeadsTemplate, parseLeadsFile };

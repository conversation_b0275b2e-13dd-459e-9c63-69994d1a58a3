# Export Leads Feature Documentation

## Overview

The Export Leads feature provides a comprehensive solution for exporting leads data to Excel files with advanced filtering, progress tracking, and user-friendly interface. The feature includes keyboard shortcuts, export history, and production-ready error handling.

## Features

### 🚀 **Core Features**
- **Excel Export**: Export leads data to properly formatted Excel files
- **Search Filtering**: Optional search query parameter for filtering exported data
- **Progress Indication**: Real-time progress tracking for large exports
- **Export History**: Track and display recent export downloads
- **Keyboard Shortcuts**: Ctrl+E for quick export access
- **Format Options**: Support for multiple export formats (Excel, CSV, PDF)
- **Error Handling**: Comprehensive error handling with user-friendly messages

### 🎯 **User Experience Features**
- **Modal Interface**: Clean, intuitive export modal
- **Search Integration**: Uses current search query as default filter
- **Progress Feedback**: Visual progress bar with status messages
- **Auto-close**: Modal closes automatically after successful export
- **History Management**: View and clear export history
- **Responsive Design**: Works on all screen sizes

## Implementation Details

### **Files Created/Modified**

#### **New Files:**
1. **`src/components/modals/ExportModal.jsx`** - Main export modal component
2. **`src/hooks/useKeyboardShortcuts.js`** - Keyboard shortcuts functionality
3. **`docs/EXPORT_LEADS_FEATURE.md`** - This documentation

#### **Modified Files:**
1. **`src/utils/excelUtils.js`** - Added export utilities and history management
2. **`src/services/leadsService.js`** - Added export API method
3. **`src/components/common/DataTable.jsx`** - Integrated export modal and shortcuts
4. **`src/pages/Hitlist.jsx`** - Updated to use new export system

### **API Integration**

#### **Backend Endpoint:**
```
GET /api/v1/leads/export
Query Parameters:
- search (optional): Filter leads by search query

Response:
- Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
- Content-Disposition: attachment; filename="leads-export-2025-01-15.xlsx"
- Binary Excel file data
```

#### **Frontend API Call:**
```javascript
// In leadsService.js
exportToExcel: async (searchQuery = '', onProgress = null) => {
  const config = {
    responseType: 'blob',
    timeout: 300000, // 5 minutes
  };
  
  if (searchQuery) {
    config.params = { search: searchQuery };
  }
  
  return await instance.get('/leads/export', config);
}
```

### **Component Architecture**

#### **ExportModal Component:**
```jsx
<ExportModal
  isOpen={isExportModalOpen}
  onClose={() => setIsExportModalOpen(false)}
  title="Export Leads"
  currentSearchQuery={searchTerm}
/>
```

**Props:**
- `isOpen`: Boolean to control modal visibility
- `onClose`: Function to close the modal
- `title`: Modal title text
- `currentSearchQuery`: Current search term to pre-fill filter

#### **Keyboard Shortcuts Hook:**
```javascript
useDataTableShortcuts({
  onExport: () => setIsExportModalOpen(true),
  onImport: () => setIsImportModalOpen(true),
  onNew: () => setIsCreateModalOpen(true),
});
```

### **Export Process Flow**

1. **User Triggers Export**
   - Click export button OR press Ctrl+E
   - Export modal opens with current search query pre-filled

2. **User Configures Export**
   - Optionally modify search filter
   - Select export format (Excel, CSV, PDF)
   - View export history if needed

3. **Export Processing**
   - Progress bar shows 0% - "Initializing export..."
   - Progress bar shows 20% - "Requesting data from server..."
   - API call to `/leads/export` with search parameter
   - Progress bar shows 50% - "Processing export..."
   - Progress bar shows 80% - "Processing file..."
   - Progress bar shows 90% - "Downloading file..."

4. **File Download**
   - Browser downloads Excel file automatically
   - Filename extracted from Content-Disposition header
   - Progress bar shows 100% - "Export completed!"

5. **History & Cleanup**
   - Export entry added to localStorage history
   - Success toast notification
   - Modal auto-closes after 1.5 seconds

### **Export History Management**

#### **Storage:**
```javascript
// Stored in localStorage as 'leads_export_history'
{
  id: 1640995200000,
  filename: "leads-export-2025-01-15.xlsx",
  searchQuery: "john doe",
  timestamp: "2025-01-15T10:30:00.000Z",
  size: null
}
```

#### **Features:**
- **Limit**: Maximum 10 entries stored
- **Display**: Filename, timestamp, search filter used
- **Management**: Clear all history option
- **Persistence**: Survives browser sessions

### **Error Handling**

#### **Network Errors:**
```javascript
// Timeout (5+ minutes)
"Export timeout. Please try again or contact support for large datasets."

// 404 Not Found
"Export endpoint not found. Please contact support."

// 403 Forbidden
"You do not have permission to export leads."

// 500+ Server Error
"Server error during export. Please try again later."

// Generic Error
"Failed to export leads. Please try again."
```

#### **User Feedback:**
- **Toast Notifications**: Success/error messages
- **Progress Status**: Real-time status updates
- **Modal State**: Prevents closing during export
- **Button States**: Disabled during processing

## Usage Instructions

### **For Users:**

#### **Basic Export:**
1. Navigate to Leads/Hitlist page
2. Click "Export Excel" button OR press `Ctrl+E`
3. Click "Export Leads" to download all leads

#### **Filtered Export:**
1. Use search box to filter leads in the table
2. Click "Export Excel" button OR press `Ctrl+E`
3. Search filter is pre-filled in export modal
4. Modify filter if needed
5. Click "Export Leads"

#### **View Export History:**
1. Open export modal
2. Click "Show Export History"
3. View recent exports with timestamps and filters
4. Click "Clear" to remove all history

#### **Keyboard Shortcuts:**
- `Ctrl+E`: Open export modal
- `Ctrl+I`: Open import modal
- `Ctrl+N`: Create new lead

### **For Developers:**

#### **Adding Export to Other Pages:**
```jsx
// 1. Import required components
import { useDataTableShortcuts } from "../hooks/useKeyboardShortcuts";

// 2. Add to DataTable props
<DataTable
  showImportExport={true}
  // onExport prop is no longer needed - handled by modal
  // ... other props
/>

// 3. Keyboard shortcuts are automatically enabled
```

#### **Customizing Export Formats:**
```javascript
// In excelUtils.js
export const getExportFormats = () => [
  { value: 'xlsx', label: 'Excel (.xlsx)', icon: '📊' },
  { value: 'csv', label: 'CSV (.csv)', icon: '📄' },
  { value: 'pdf', label: 'PDF (.pdf)', icon: '📋' },
  // Add more formats here
];
```

#### **Custom Progress Handling:**
```javascript
const customExport = async () => {
  await exportLeadsToExcel(
    searchQuery,
    (progress, status) => {
      console.log(`${progress}%: ${status}`);
      // Custom progress handling
    }
  );
};
```

## Testing

### **Manual Testing Checklist:**
- [ ] Export button opens modal
- [ ] Ctrl+E keyboard shortcut works
- [ ] Search filter pre-fills from table search
- [ ] Progress bar shows during export
- [ ] File downloads successfully
- [ ] Export history updates
- [ ] Error handling works for network issues
- [ ] Modal closes after successful export
- [ ] History can be viewed and cleared

### **Test Scenarios:**
1. **Large Dataset**: Test with 1000+ leads
2. **Network Issues**: Test with slow/failed connections
3. **Search Filters**: Test various search queries
4. **Multiple Exports**: Test rapid successive exports
5. **Browser Compatibility**: Test across different browsers

## Performance Considerations

### **Optimizations:**
- **Blob Handling**: Efficient binary data processing
- **Memory Management**: Proper cleanup of object URLs
- **Timeout Handling**: 5-minute timeout for large exports
- **Progress Feedback**: Prevents user confusion during long exports

### **Limitations:**
- **File Size**: Large exports may take several minutes
- **Browser Memory**: Very large files may cause memory issues
- **Network**: Slow connections may timeout
- **Storage**: Export history limited to 10 entries

## Future Enhancements

### **Planned Features:**
1. **Batch Export**: Export multiple filtered sets
2. **Scheduled Exports**: Automated recurring exports
3. **Email Delivery**: Send exports via email
4. **Custom Templates**: User-defined export formats
5. **Compression**: ZIP files for large exports
6. **Cloud Storage**: Direct upload to cloud services

### **Technical Improvements:**
1. **Streaming**: Stream large exports to reduce memory usage
2. **Caching**: Cache export results for repeated requests
3. **Background Processing**: Move exports to background workers
4. **Real-time Updates**: WebSocket progress updates

## Support

For technical support or feature requests regarding the export functionality, please:
1. Check the console for detailed error logs
2. Verify network connectivity and API availability
3. Test with smaller datasets to isolate issues
4. Contact the development team with specific error messages

## Security Considerations

- **Data Access**: Exports respect user permissions
- **File Security**: No sensitive data in filenames
- **History Privacy**: Export history stored locally only
- **Network Security**: HTTPS required for production
- **Input Validation**: Search queries are sanitized

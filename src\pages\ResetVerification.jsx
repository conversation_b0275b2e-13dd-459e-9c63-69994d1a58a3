import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { InputOtp } from "primereact/inputotp";
import { Button } from "primereact/button";
import PublicLayout from "../components/layouts/PublicLayout";
import logoSmall from "../assets/images/logo.png";
import logoText from "../assets/images/logo-text.png";

const ResetVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [token, setToken] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [apiError, setApiError] = useState("");
  const [email, setEmail] = useState("");

  useEffect(() => {
    // Get email from navigation state
    if (location.state?.email) {
      setEmail(location.state.email);
    } else {
      // If no email in state, redirect back to request reset
      navigate("/forgot-password");
    }
  }, [location.state, navigate]);

  const customInput = ({ events, props }) => {
    return (
      <>
        <input
          {...events}
          {...props}
          type="text"
          className="custom-otp-input"
        />
        {props.id === 2 && (
          <div className="px-3">
            <i className="pi pi-minus" />
          </div>
        )}
      </>
    );
  };

  const handleSubmit = async () => {
    setApiError("");

    if (!token || token.length !== 6) {
      setApiError("Please enter the complete 6-digit code");
      return;
    }

    setIsLoading(true);

    try {
      // Log the submitted data to console for now
      console.log("OTP Verification Data:", { email, otp: token });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Navigate to reset password page
      navigate("/reset-password", { state: { email, token } });
    } catch (err) {
      let errorMessage = "Invalid verification code. Please try again.";

      if (err.response) {
        const status = err.response.status;
        if (status === 400) {
          errorMessage = "Invalid or expired verification code.";
        } else if (status === 500) {
          errorMessage = "Server error. Please try again later.";
        } else {
          errorMessage =
            err.response.data?.message ||
            "Invalid verification code. Please try again.";
        }
      } else if (err.request) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else {
        errorMessage =
          err.message || "Invalid verification code. Please try again.";
      }

      setApiError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setApiError("");
    setIsResending(true);

    try {
      // Log the resend request to console for now
      console.log("Resend OTP Request:", { email });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Show success message (you could use toast here)
      alert("Verification code sent successfully!");
    } catch (err) {
      setApiError("Failed to resend code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg border-0">
            {/* Logo Section */}
            <div className="flex items-center justify-center h-24 px-4">
              <div className="flex items-center gap-4">
                <span className="h-11 flex-shrink-0">
                  <img
                    className="logo-abbr h-full w-auto"
                    src={logoSmall}
                    alt=""
                  />
                </span>

                <span className="h-10 transition-opacity duration-300">
                  <img
                    className="brand-title h-full w-auto"
                    src={logoText}
                    alt=""
                  />
                </span>
              </div>
            </div>

            {/* Verification Form */}
            <div className="p-6">
              <div className="flex flex-col items-center">
                <h2 className="text-xl font-semibold text-center text-gray-900 dark:text-white mb-2">
                  Check Your Email
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-6">
                  We've sent a one-time password (OTP) to{" "}
                  <strong>{email}</strong>.
                  <br />
                  Please enter it below to continue.
                </p>

                {apiError && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm mb-4 w-full">
                    {apiError}
                  </div>
                )}

                <InputOtp
                  value={token}
                  onChange={(e) => setToken(e.value)}
                  length={6}
                  inputTemplate={customInput}
                  style={{ gap: 4 }}
                  className="mb-6 flex flex-col w-full bg-red-200"
                />

                <div className="flex justify-between mt-5 w-full">
                  <Button
                    label={isResending ? "Sending..." : "Resend Code"}
                    link
                    className="p-0 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                    onClick={handleResendCode}
                    disabled={isResending}
                  />
                  <Button
                    label={isLoading ? "Verifying..." : "Submit Code"}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white border-green-600 hover:border-green-700"
                    onClick={handleSubmit}
                    disabled={isLoading || !token || token.length !== 6}
                  />
                </div>

                <div className="text-center mt-4">
                  <button
                    type="button"
                    onClick={() => navigate("/forgot-password")}
                    className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 font-medium transition-colors duration-200 text-sm"
                  >
                    Back to Request Reset
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default ResetVerification;

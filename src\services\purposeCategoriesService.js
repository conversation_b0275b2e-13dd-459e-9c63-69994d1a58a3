import instance from '../axios/instance.jsx';

// Purpose Categories API endpoints
const ENDPOINTS = {
  PURPOSE_CATEGORIES: '/purpose-categories',
  PURPOSE_CATEGORY_BY_ID: (id) => `/purpose-categories/${id}`,
  PURPOSE_CATEGORIES_STATISTICS: '/purpose-categories/statistics',
};

// Purpose Categories Service
export const purposeCategoriesService = {
  // Get all purpose categories
  getAll: async (searchQuery = '') => {
    try {
      console.log('=== FETCHING ALL PURPOSE CATEGORIES ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.PURPOSE_CATEGORIES}`);
      if (searchQuery) {
        console.log(`Search Query: ${searchQuery}`);
      }
      
      const config = {};
      if (searchQuery && searchQuery.trim()) {
        config.params = { search: searchQuery.trim() };
      }
      
      const response = await instance.get(ENDPOINTS.PURPOSE_CATEGORIES, config);
      
      console.log('Purpose categories response:', response.data);
      console.log('Total categories:', response.data.total);
      console.log('=======================================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching purpose categories:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose categories endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view purpose categories.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching purpose categories. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch purpose categories.');
      }
    }
  },

  // Create a new purpose category
  create: async (categoryData) => {
    try {
      console.log('=== CREATING NEW PURPOSE CATEGORY ===');
      console.log('Category data:', categoryData);
      console.log(`API Endpoint: POST ${ENDPOINTS.PURPOSE_CATEGORIES}`);
      
      const response = await instance.post(ENDPOINTS.PURPOSE_CATEGORIES, categoryData);
      
      console.log('Category creation response:', response.data);
      console.log('=====================================');
      
      return response.data;
    } catch (error) {
      console.error('Error creating purpose category:', error);
      
      // Handle different error types
      if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid category data provided.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to create purpose categories.');
      } else if (error.response?.status === 409) {
        throw new Error('A category with this name already exists.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while creating category. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to create purpose category.');
      }
    }
  },

  // Update an existing purpose category
  update: async (id, categoryData) => {
    try {
      console.log('=== UPDATING PURPOSE CATEGORY ===');
      console.log(`Category ID: ${id}`);
      console.log('Updated data:', categoryData);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.PURPOSE_CATEGORY_BY_ID(id)}`);
      
      const response = await instance.patch(ENDPOINTS.PURPOSE_CATEGORY_BY_ID(id), categoryData);
      
      console.log('Category update response:', response.data);
      console.log('=================================');
      
      return response.data;
    } catch (error) {
      console.error('Error updating purpose category:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose category not found. It may have been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this category.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid category data provided.');
      } else if (error.response?.status === 409) {
        throw new Error('A category with this name already exists.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while updating category. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update purpose category.');
      }
    }
  },

  // Delete a purpose category
  delete: async (id) => {
    try {
      console.log('=== DELETING PURPOSE CATEGORY ===');
      console.log(`Category ID: ${id}`);
      console.log(`API Endpoint: DELETE ${ENDPOINTS.PURPOSE_CATEGORY_BY_ID(id)}`);
      
      const response = await instance.delete(ENDPOINTS.PURPOSE_CATEGORY_BY_ID(id));
      
      console.log('Category deletion response:', response.status);
      console.log('=================================');
      
      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting purpose category:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose category not found. It may have already been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this category.');
      } else if (error.response?.status === 409) {
        throw new Error('Cannot delete category. It is currently being used by purposes.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while deleting category. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to delete purpose category.');
      }
    }
  },

  // Get a single purpose category by ID
  getById: async (id) => {
    try {
      console.log('=== FETCHING PURPOSE CATEGORY BY ID ===');
      console.log(`Category ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.PURPOSE_CATEGORY_BY_ID(id)}`);
      
      const response = await instance.get(ENDPOINTS.PURPOSE_CATEGORY_BY_ID(id));
      
      console.log('Category details response:', response.data);
      console.log('=======================================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching purpose category by ID:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Purpose category not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view this category.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching category. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch category details.');
      }
    }
  },

  // Get purpose categories statistics
  getStatistics: async () => {
    try {
      console.log('=== FETCHING PURPOSE CATEGORIES STATISTICS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.PURPOSE_CATEGORIES_STATISTICS}`);
      
      const response = await instance.get(ENDPOINTS.PURPOSE_CATEGORIES_STATISTICS);
      
      console.log('Categories statistics response:', response.data);
      console.log('===============================================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching purpose categories statistics:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Statistics endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view statistics.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching statistics. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch statistics.');
      }
    }
  },
};

/**
 * Format purpose categories data for table display
 * @param {Object} apiResponse - Response from purpose categories API
 * @returns {Array} - Formatted data for DataTable
 */
export const formatPurposeCategoriesForTable = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((category, index) => ({
    id: category.id || `category${index + 1}`,
    name: category.name || "Unknown Category",
    addedBy: category.created_by?.name || category.added_by || "Unknown",
    addedOn: formatCategoryDate(category.created_at),
    // Additional fields for detailed view
    description: category.description,
    purposesCount: category.purposes_count || 0,
    isInUse: category.is_in_use || false,
    createdAt: category.created_at,
  }));
};

/**
 * Format purpose categories for dropdown selection
 * @param {Object} apiResponse - Response from purpose categories API
 * @returns {Array} - Formatted options for Select component
 */
export const formatCategoriesForDropdown = (apiResponse) => {
  if (!apiResponse || !apiResponse.data) {
    return [];
  }

  return apiResponse.data.map((category) => ({
    value: category.id,
    label: category.name,
    description: category.description,
    purposesCount: category.purposes_count || 0,
  }));
};

/**
 * Format category date for display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
const formatCategoryDate = (dateString) => {
  if (!dateString) return "Unknown date";
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (error) {
    console.warn('Error formatting category date:', dateString);
    return "Invalid date";
  }
};

/**
 * Get usage status color for purpose categories
 * @param {boolean} isInUse - Whether category is in use
 * @param {number} purposesCount - Number of purposes in category
 * @returns {string} - Color classes
 */
export const getCategoryUsageColor = (isInUse, purposesCount = 0) => {
  if (!isInUse || purposesCount === 0) {
    return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
  } else if (purposesCount >= 10) {
    return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
  } else if (purposesCount >= 5) {
    return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
  } else {
    return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
  }
};

export default purposeCategoriesService;

import useCountUp from "../../hooks/useCountUp";

function DashboardCard({
  title,
  value,
  description,
  icon,
  iconColor,
  duration = 2000,
  delay = 0,
}) {
  const animatedValue = useCountUp(value, duration, delay);

  return (
    <div className="relative bg-white rounded-xl px-5 py-5 shadow-lg border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600 uppercase tracking-wide">
          {title}
        </h3>
        <div className="flex items-center h-full absolute top-0 right-6">
          <div
            className="w-8 h-8 flex items-center justify-center"
            style={{ color: iconColor }}
          >
            {icon}
          </div>
        </div>
      </div>
      <div className="text-[28px] font-semibold text-gray-900 mb-1">
        {animatedValue.toLocaleString()}
      </div>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  );
}

export default DashboardCard;

import { useState, useEffect } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import LoanFollowUpForm from "../components/forms/LoanFollowUpForm";
import RescheduleForm from "../components/forms/RescheduleForm";
import LogOutcomeForm from "../components/forms/LogOutcomeForm";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import { loanService, formatLoanFollowUpsForTable } from "../services/loanService";
import { toast } from 'react-toastify';

const LoanFollowUps = () => {
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [followUps, setFollowUps] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);

  // Define table columns
  const columns = [
    {
      key: "customerName",
      title: "CUSTOMER NAME",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "loanId",
      title: "LOAN ID",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "followUpType",
      title: "FOLLOW-UP TYPE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "followUpReason",
      title: "REASON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "scheduledDate",
      title: "SCHEDULED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "status",
      title: "STATUS",
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === "Completed" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" :
          value === "Pending" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400" :
          value === "Canceled" ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400" :
          "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"
        }`}>
          {value}
        </span>
      ),
    },
    {
      key: "assignedOfficer",
      title: "ASSIGNED OFFICER",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
    {
      key: "createdDate",
      title: "CREATED DATE",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch follow-ups data from API
  const fetchFollowUps = async () => {
    try {
      setLoading(true);
      console.log("Fetching loan follow-ups data...");

      const response = await loanService.followUps.getAll();
      const formattedFollowUps = formatLoanFollowUpsForTable(response);

      setFollowUps(formattedFollowUps);
      console.log("Loan follow-ups data loaded successfully:", formattedFollowUps.length, "follow-ups");
    } catch (error) {
      console.error("Error fetching loan follow-ups:", error);
      toast.error(error.message || "Failed to load loan follow-ups data");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchFollowUps();
  }, []);

  // Handle load more
  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate loading more data
    setTimeout(() => {
      setLoadingMore(false);
    }, 1000);
  };

  // Handle create follow-up
  const handleCreateSubmit = async (formData) => {
    try {
      console.log("Creating new loan follow-up:", formData);
      const newFollowUp = await loanService.followUps.create(formData);
      
      // Add the new follow-up to the state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [newFollowUp] })[0];
      setFollowUps(prevFollowUps => [formattedFollowUp, ...prevFollowUps]);
      
      toast.success("Loan follow-up created successfully!");
      return true;
    } catch (error) {
      console.error("Error creating loan follow-up:", error);
      toast.error(error.message || "Failed to create loan follow-up");
      return false;
    }
  };

  // Handle reschedule follow-up
  const handleRescheduleSubmit = async (formData) => {
    try {
      console.log("Rescheduling loan follow-up:", formData);
      const updatedFollowUp = await loanService.followUps.update(selectedItem.id, formData);
      
      // Update the follow-up in state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [updatedFollowUp] })[0];
      setFollowUps(prevFollowUps => 
        prevFollowUps.map(followUp => 
          followUp.id === selectedItem.id ? formattedFollowUp : followUp
        )
      );
      
      toast.success("Loan follow-up rescheduled successfully!");
      return true;
    } catch (error) {
      console.error("Error rescheduling loan follow-up:", error);
      toast.error(error.message || "Failed to reschedule loan follow-up");
      return false;
    }
  };

  // Handle log outcome
  const handleLogOutcomeSubmit = async (formData) => {
    try {
      console.log("Logging outcome for loan follow-up:", formData);
      const updatedFollowUp = await loanService.followUps.update(selectedItem.id, {
        ...formData,
        status: "completed"
      });
      
      // Update the follow-up in state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [updatedFollowUp] })[0];
      setFollowUps(prevFollowUps => 
        prevFollowUps.map(followUp => 
          followUp.id === selectedItem.id ? formattedFollowUp : followUp
        )
      );
      
      toast.success("Outcome logged successfully!");
      return true;
    } catch (error) {
      console.error("Error logging outcome:", error);
      toast.error(error.message || "Failed to log outcome");
      return false;
    }
  };

  // Handle cancel follow-up
  const handleCancelConfirm = async (followUp) => {
    try {
      console.log("Canceling loan follow-up:", followUp);
      const updatedFollowUp = await loanService.followUps.update(followUp.id, {
        status: "canceled"
      });
      
      // Update the follow-up in state
      const formattedFollowUp = formatLoanFollowUpsForTable({ data: [updatedFollowUp] })[0];
      setFollowUps(prevFollowUps => 
        prevFollowUps.map(f => 
          f.id === followUp.id ? formattedFollowUp : f
        )
      );
      
      toast.success("Loan follow-up canceled successfully!");
    } catch (error) {
      console.error("Error canceling loan follow-up:", error);
      toast.error(error.message || "Failed to cancel loan follow-up");
    }
  };

  // Handle view follow-up
  const handleView = (followUp) => {
    console.log("View loan follow-up:", followUp);
    setSelectedItem(followUp);
  };

  return (
    <PrivateLayout pageTitle="Loan Follow-ups">
      <div className="">
        {/* Data Table */}
        <DataTable
          columns={columns}
          data={followUps}
          searchPlaceholder="Search loan follow-ups..."
          addButtonText="New Follow-up"
          onView={handleView}
          actions={["cancel", "log-outcome", "reschedule"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Follow-ups"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="status"
          highlightColors={{
            Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
            Completed: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            Canceled: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
          }}
          // Data count
          showDataCount={true}
          dataCountLabel="loan follow-ups"
          // Modal forms
          createForm={({ onClose }) => (
            <LoanFollowUpForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <RescheduleForm
              item={item}
              onClose={onClose}
              onSubmit={handleRescheduleSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleCancelConfirm}
              itemName="Loan Follow-up"
              confirmText="Cancel"
              confirmButtonText="Cancel Follow-up"
              title={`Cancel Follow-up for ${item?.customerName}`}
              message="Are you sure you want to cancel this follow-up? This action cannot be undone."
            />
          )}
          logOutcomeForm={({ item, onClose }) => (
            <LogOutcomeForm
              item={item}
              onClose={onClose}
              onSubmit={handleLogOutcomeSubmit}
            />
          )}
          createModalTitle="Create New Loan Follow-up"
          editModalTitle={
            selectedItem
              ? `Reschedule Follow-up for ${selectedItem.customerName}`
              : "Reschedule Follow-up"
          }
          deleteModalTitle=""
          logOutcomeModalTitle={
            selectedItem
              ? `Log Outcome for ${selectedItem.customerName}`
              : "Log Outcome"
          }
          modalSize="lg"
          deleteModalSize="sm"
          logOutcomeModalSize="md"
        />
      </div>
    </PrivateLayout>
  );
};

export default LoanFollowUps;

import { useEffect, useState } from "react";

const SuccessModal = ({
  isOpen,
  onClose,
  title = "Success",
  message = "Operation completed successfully",
}) => {
  const [showAnimation, setShowAnimation] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Reset animation state
      setShowAnimation(false);
      // Trigger animation after modal opens
      const timer = setTimeout(() => {
        setShowAnimation(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-[rgba(0,0,0,0.7)] flex items-center justify-center p-4 z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
        <style scoped>
          {`
            @keyframes checkmark-draw {
              0% {
                stroke-dasharray: 0 100;
                opacity: 0;
              }
              50% {
                opacity: 1;
              }
              100% {
                stroke-dasharray: 100 0;
                opacity: 1;
              }
            }

            @keyframes checkmark-scale {
              0% {
                transform: scale(0);
              }
              50% {
                transform: scale(1.1);
              }
              100% {
                transform: scale(1);
              }
            }

            @keyframes circle {
              0% {
                transform: scale(0);
                opacity: 0;
              }
              50% {
                transform: scale(1.1);
                opacity: 1;
              }
              100% {
                transform: scale(1);
                opacity: 1;
              }
            }

            .success-circle {
              width: 80px;
              height: 80px;
              border-radius: 50%;
              background: linear-gradient(135deg, #10b981, #059669);
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 0 auto 1.5rem;
              position: relative;
              box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
            }

            .success-circle::before {
              content: '';
              position: absolute;
              width: 100px;
              height: 100px;
              border-radius: 50%;
              background: rgba(16, 185, 129, 0.1);
              animation: circle 0.6s ease-out;
            }

            .success-circle.animate {
              animation: circle 0.6s ease-out;
            }

            .checkmark-container {
              position: relative;
              z-index: 1;
              width: 40px;
              height: 40px;
            }

            .checkmark-container.animate {
              animation: checkmark-scale 0.6s ease-out 0.3s both;
            }

            .checkmark-svg {
              width: 100%;
              height: 100%;
            }

            .checkmark-path {
              fill: none;
              stroke: white;
              stroke-width: 6;
              stroke-linecap: round;
              stroke-linejoin: round;
              stroke-dasharray: 100;
              stroke-dashoffset: 100;
            }

            .checkmark-path.animate {
              animation: checkmark-draw 0.8s ease-out 0.5s both;
            }
          `}
        </style>

        <div className="text-center">
          {/* Animated Success Icon */}
          <div className={`success-circle ${showAnimation ? "animate" : ""}`}>
            <div
              className={`checkmark-container ${
                showAnimation ? "animate" : ""
              }`}
            >
              <svg className="checkmark-svg" viewBox="0 0 100 100">
                <path
                  className={`checkmark-path ${showAnimation ? "animate" : ""}`}
                  d="M20,50 L40,70 L80,30"
                />
              </svg>
            </div>
          </div>

          {/* Success Message */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {title}
            </h3>
          </div>

          <div>
            <p className="text-gray-600 dark:text-gray-400 mb-6">{message}</p>
          </div>

          {/* Action Button */}
          <div className="flex justify-between">
            <button></button>
            <button
              onClick={onClose}
              className="w- bg-gray-400 hover:opacity-[0.8] text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform focus:outline-none focus:ring-4 focus:ring-green-200 dark:focus:ring-green-800"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;

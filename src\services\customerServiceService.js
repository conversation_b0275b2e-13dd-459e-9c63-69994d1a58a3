import instance from "../axios/instance";

export const customerServiceService = {
  // Fetch hitlists
  getHitlists: async () => {
    try {
      const response = await instance.get("/customer-service/hitlists");
      return response.data;
    } catch (error) {
      console.error("Error fetching hitlists:", error);
      throw error;
    }
  },

  // Fetch hitlist details by code
  getHitlistDetails: async (hitlistCode) => {
    try {
      const response = await instance.get(`/customer-service/target/${hitlistCode}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching hitlist details:", error);
      throw error;
    }
  },

  // Fetch customer feedback categories
  getCustomerFeedbackCategories: async () => {
    try {
      const response = await instance.get("/customer-service/customer-feedback-categories");
      return response.data;
    } catch (error) {
      console.error("Error fetching customer feedback categories:", error);
      throw error;
    }
  },

  // Get hitlist template URL (for ImportModal)
  getHitlistTemplateUrl: () => {
    // Return the actual template download endpoint
    return `${instance.defaults.baseURL}/customer-service/hitlist-template`;
  },

  // Fetch calls to do
  getCallsToDo: async () => {
    try {
      const response = await instance.get("/customer-service/calls-to-do");
      return response.data;
    } catch (error) {
      console.error("Error fetching calls to do:", error);
      throw error;
    }
  },

  // Import hitlist
  importHitlist: async (formData) => {
    try {
      const response = await instance.post("/customer-service/hitlist-template", formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      console.error("Error importing hitlist:", error);
      throw error;
    }
  },

  // Get all hitlist records
  getAllHitlistRecords: async () => {
    try {
      const response = await instance.get("/customer-service/hitlist-records");
      return response.data;
    } catch (error) {
      console.error("Error fetching all hitlist records:", error);
      throw error;
    }
  },

  // Make a call (for calls-to-do)
  makeCall: async (callToDoId, callData) => {
    try {
      const response = await instance.post(`/customer-service/calls-to-do/${callToDoId}/make-call`, callData);
      return response.data;
    } catch (error) {
      console.error("Error making call:", error);
      throw error;
    }
  },

  // Get all hitlist records across all hitlists
  getAllHitlistRecords: async () => {
    try {
      const response = await instance.get("/customer-service/hitlist-records");
      return response.data;
    } catch (error) {
      console.error("Error fetching all hitlist records:", error);
      throw error;
    }
  },

  // Download hitlist template
  downloadHitlistTemplate: () => {
    // Create anchor tag for download to avoid page flash
    const link = document.createElement('a');
    link.href = `${instance.defaults.baseURL}/customer-service/hitlist-template`;
    link.download = 'hitlist-template.xlsx';
    link.style.display = 'none'; // Hide the link
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  // Update a hitlist record
  updateHitlistRecord: async (id, data) => {
    try {
      const response = await instance.patch(`/customer-service/hitlist-record/${id}`, data);
      return response.data;
    } catch (error) {
      console.error("Error updating hitlist record:", error);
      throw error;
    }
  },

  // Delete a hitlist record
  deleteHitlistRecord: async (id) => {
    try {
      const response = await instance.delete(`/customer-service/hitlist-record/${id}`);
      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error("Error deleting hitlist record:", error);
      throw error;
    }
  },

  // Reassign a hitlist record
  reassignHitlistRecord: async (id, data) => {
    try {
      const response = await instance.patch(`/customer-service/hitlist-record/${id}/reassign`, data);
      return response.data;
    } catch (error) {
      console.error("Error reassigning hitlist record:", error);
      throw error;
    }
  },
};
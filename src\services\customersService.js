import instance from "../axios/instance";

// Customers service for API calls
export const customersService = {
    // Get all customers
    getAll: async (params = {}) => {
        try {
            const response = await instance.get("/customers", { params });
            return response.data;
        } catch (error) {
            console.error("Error fetching customers:", error);
            throw error;
        }
    },

    // Get customer by ID
    getById: async (id) => {
        try {
            const response = await instance.get(`/customers/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching customer ${id}:`, error);
            throw error;
        }
    },

    // Create new customer
    create: async (customerData) => {
        try {
            const response = await instance.post("/customers", customerData);
            return response.data;
        } catch (error) {
            console.error("Error creating customer:", error);
            throw error;
        }
    },

    // Update customer
    update: async (id, customerData) => {
        try {
            const response = await instance.put(`/customers/${id}`, customerData);
            return response.data;
        } catch (error) {
            console.error(`Error updating customer ${id}:`, error);
            throw error;
        }
    },

    // Delete customer
    delete: async (id) => {
        try {
            const response = await instance.delete(`/customers/${id}`);
            return response.status === 204;
        } catch (error) {
            console.error(`Error deleting customer ${id}:`, error);
            throw error;
        }
    },
};

// Format customers response for consistent data structure
export const formatCustomersResponse = (response) => {
    console.log("Raw customers response:", response);

    if (!response) {
        console.warn("Invalid customers response format:", response);
        return [];
    }

    // Handle different response formats
    let customersData;
    if (Array.isArray(response)) {
        customersData = response;
    } else if (response.data && Array.isArray(response.data)) {
        customersData = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        customersData = response.data.data;
    } else {
        console.warn("Could not find customers array in response:", response);
        return [];
    }

    console.log("Extracted customers data:", customersData);

    return customersData.map((customer) => ({
        id: customer.id,
        clientId: customer.client_id || customer.clientId || "N/A",
        accountNumber: customer.account_number || customer.accountNumber || "N/A",
        name: customer.customer_name || customer.name || customer.lead_name || "Unknown Customer",
        phoneNumber: customer.phone_number || customer.phoneNumber || customer.mobile || "N/A",
        branch: customer.branch?.name || customer.branch_name || "N/A",
        branchId: customer.branch?.id || customer.branch_id,
        email: customer.email || "",
        address: customer.address || "",
        status: customer.status || "Active",
        createdAt: customer.created_at || customer.createdAt,
        updatedAt: customer.updated_at || customer.updatedAt,
        // Keep original data for reference
        originalData: customer,
    }));
};

// Format customers for table display
export const formatCustomersForTable = (customers) => {
    return customers.map((customer) => ({
        ...customer,
        // Ensure all required fields are present for table display
        clientId: customer.clientId || "-",
        accountNumber: customer.accountNumber || "-",
        name: customer.name || "Unknown Customer",
        phoneNumber: customer.phoneNumber || "-",
        branch: customer.branch || "-",
    }));
};
import { useState } from "react";
import { X, Upload, Download } from "lucide-react";
import Select from "react-select";
import { customerServiceService } from "../../services/customerServiceService";

const ImportModal = ({
  isOpen,
  onClose,
  title = "Import Data",
  templateFileName = "template.xlsx",
  onImport,
  acceptedFileTypes = ".xlsx,.xls,.csv",
  // New props for anchor selection
  showAnchorSelection = false,
  anchors = [],

  isLeadsImport = false,
  // New props for hitlist type selection
  showHitlistTypeSelection = false,
  isHitlistImport = false,
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [selectedAnchor, setSelectedAnchor] = useState(null);
  const [selectedHitlistType, setSelectedHitlistType] = useState({
    value: "2by2by2",
    label: "2by2by2",
  });
  const [isProcessing, setIsProcessing] = useState(false);

  // Hitlist type options
  const hitlistTypeOptions = [
    { value: "2by2by2", label: "2by2by2" },
    { value: "Dormancy", label: "Dormancy" },
  ];

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    setSelectedFile(file);
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0]);
    }
  };

  const handleImport = async () => {
    if (selectedFile && onImport) {
      // For leads import, require anchor selection
      if (isLeadsImport && !selectedAnchor) {
        alert("Please select an anchor before importing leads.");
        return;
      }

      // For hitlist import, require hitlist type selection
      if (isHitlistImport && !selectedHitlistType) {
        alert("Please select a hitlist type before importing.");
        return;
      }

      setIsProcessing(true);
      try {
        if (isHitlistImport) {
          // Create FormData for hitlist import
          const formData = new FormData();
          formData.append("file", selectedFile);
          formData.append("type", selectedHitlistType.value);

          // Pass the FormData to parent component to handle the request
          await onImport(formData);
        } else {
          // Handle other types of imports (e.g., leads)
          await onImport(selectedFile, selectedAnchor?.value || selectedAnchor);
        }

        setSelectedFile(null);
        setSelectedAnchor(null);
        setSelectedHitlistType(null);
        onClose();
      } catch (error) {
        console.error("Import failed:", error);
        alert(
          error.response?.data?.message ||
            error.message ||
            "Import failed. Please check the file format and try again."
        );
      } finally {
        setIsProcessing(false);
      }
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setIsProcessing(false);
    onClose();
  };

  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused ? "#10b981" : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
    menuPlacement: "top", // Always open upwards
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[rgba(0,0,0,0.7)] flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-xl mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Download Template Section */}
          <div className="mb-6 flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 ">
              Import File
            </label>
            <button
              type="button"
              onClick={() => customerServiceService.downloadHitlistTemplate()}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              <Download className="h-4 w-4 mr-1" />
              Download Template
            </button>
          </div>

          {/* File Upload Section */}
          <div
            className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                : "border-gray-300 dark:border-gray-600"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              accept={acceptedFileTypes}
              onChange={handleFileSelect}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            />

            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />

            {selectedFile ? (
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedFile.name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            ) : (
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  <span className="font-medium text-blue-600 dark:text-blue-400">
                    Choose File
                  </span>{" "}
                  or drag and drop
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {templateFileName}
                </p>
              </div>
            )}
          </div>

          {/* Hitlist Type Selection Section - Only for hitlist import */}
          {showHitlistTypeSelection && isHitlistImport && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Hitlist Type *
              </label>
              <div className="flex space-x-6">
                {hitlistTypeOptions.map((option) => (
                  <div key={option.value} className="flex items-center">
                    <input
                      id={`hitlist-type-${option.value}`}
                      name="hitlistType"
                      type="radio"
                      value={option.value}
                      checked={
                        selectedHitlistType?.value === option.value ||
                        selectedHitlistType === option.value
                      }
                      onChange={(e) =>
                        setSelectedHitlistType({
                          value: e.target.value,
                          label: option.label,
                        })
                      }
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                    />
                    <label
                      htmlFor={`hitlist-type-${option.value}`}
                      className="ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                Select the type of hitlist you are importing.
              </p>
            </div>
          )}

          {/* Anchor Selection Section - Only for leads import */}
          {showAnchorSelection && isLeadsImport && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Assign Anchor to All Leads *
              </label>
              <Select
                value={selectedAnchor}
                onChange={setSelectedAnchor}
                options={anchors}
                styles={selectStyles}
                placeholder="Search and select an anchor..."
                isSearchable
                className="react-select-container"
                classNamePrefix="react-select"
                noOptionsMessage={() => "No anchors found"}
                menuPlacement="top"
                menuPosition="fixed"
                maxMenuHeight={200}
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                This anchor will be assigned to all leads in the imported file.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            Close
          </button>
          <button
            onClick={handleImport}
            disabled={
              !selectedFile ||
              isProcessing ||
              (isLeadsImport && showAnchorSelection && !selectedAnchor)
            }
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              "Import Data"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImportModal;

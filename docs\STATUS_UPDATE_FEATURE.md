# Lead Status Update Feature Documentation

## Overview

The Lead Status Update feature provides a comprehensive solution for updating lead statuses through an intuitive actions dialog with immediate UI feedback, optimistic updates, and robust error handling. The feature includes visual loading indicators, keyboard navigation, and accessibility support.

## Features

### 🚀 **Core Features**
- **Status Update API Integration**: PATCH /leads/:id with leadStatus in request body
- **Actions Dialog**: 4 status options (Pending, Warm, Hot, Cold) with descriptions
- **Optimistic Updates**: Immediate UI updates with rollback on error
- **Visual Feedback**: Loading indicators and success/error messages
- **Keyboard Navigation**: Arrow keys, Enter, and Escape support
- **Accessibility**: ARIA labels and screen reader support

### 🎯 **User Experience Features**
- **Immediate Response**: UI updates instantly when status is selected
- **Loading States**: Spinner in status badge and disabled row during update
- **Error Handling**: User-friendly error messages with automatic rollback
- **Visual Consistency**: Color-coded status badges with consistent styling
- **Auto-close**: Modal closes automatically on successful update

## Implementation Details

### **Files Created/Modified**

#### **New Files:**
1. **`src/components/modals/StatusUpdateModal.jsx`** - Main status update modal component
2. **`docs/STATUS_UPDATE_FEATURE.md`** - This documentation

#### **Modified Files:**
1. **`src/services/leadsService.js`** - Added updateStatus method with error handling
2. **`src/components/common/DataTable.jsx`** - Added status action handling and loading states
3. **`src/pages/Hitlist.jsx`** - Integrated status update functionality with optimistic updates

### **API Integration**

#### **Backend Endpoint:**
```
PATCH /api/v1/leads/:id
Content-Type: application/json

Request Body:
{
  "leadStatus": "Hot"
}

Response:
{
  "id": "lead-uuid",
  "status": "Hot",
  // ... other lead fields
}
```

#### **Frontend API Call:**
```javascript
// In leadsService.js
updateStatus: async (id, status) => {
  const response = await instance.patch(`/leads/${id}`, {
    leadStatus: status
  });
  return response.data;
}
```

### **Component Architecture**

#### **StatusUpdateModal Component:**
```jsx
<StatusUpdateModal
  isOpen={isStatusModalOpen}
  onClose={handleCloseStatusModal}
  lead={selectedLeadForStatus}
  onStatusUpdate={handleStatusUpdate}
  title="Update Lead Status"
/>
```

**Props:**
- `isOpen`: Boolean to control modal visibility
- `onClose`: Function to close the modal
- `lead`: Lead object with current status
- `onStatusUpdate`: Function to handle status updates
- `title`: Modal title text

#### **Status Options:**
```javascript
const statusOptions = [
  {
    value: 'Pending',
    color: '#ffb800',
    description: 'Lead is awaiting initial contact or response'
  },
  {
    value: 'Warm',
    color: '#369dc9',
    description: 'Lead has shown interest and is engaged'
  },
  {
    value: 'Hot',
    color: '#ff0000',
    description: 'Lead is highly interested and ready to convert'
  },
  {
    value: 'Cold',
    color: '#1c5b41',
    description: 'Lead has low interest or is unresponsive'
  }
];
```

### **Status Update Flow**

1. **User Triggers Update**
   - Click status action in DataTable actions dropdown
   - StatusUpdateModal opens with current lead data

2. **User Selects Status**
   - Click status option or use arrow keys to navigate
   - Visual feedback shows selected status
   - Descriptions help users understand each status

3. **Optimistic Update**
   - UI immediately updates status badge in table
   - Loading spinner appears in status badge
   - Table row becomes semi-transparent and non-interactive

4. **API Call**
   - PATCH request sent to `/leads/:id` with new status
   - Comprehensive error handling for different scenarios

5. **Success/Error Handling**
   - **Success**: Keep optimistic update, show success toast, close modal
   - **Error**: Rollback to previous status, show error message, keep modal open

### **State Management**

#### **Optimistic Updates:**
```javascript
// Immediate UI update
setLeads(prevLeads => 
  prevLeads.map(lead => 
    lead.id === leadId 
      ? { ...lead, status: newStatus }
      : lead
  )
);

// Rollback on error
setLeads(prevLeads => 
  prevLeads.map(lead => 
    lead.id === leadId 
      ? { ...lead, status: originalStatus }
      : lead
  )
);
```

#### **Loading States:**
```javascript
// Track leads being updated
const [updatingLeadIds, setUpdatingLeadIds] = useState(new Set());

// Add to updating set
setUpdatingLeadIds(prev => new Set(prev).add(leadId));

// Remove from updating set
setUpdatingLeadIds(prev => {
  const newSet = new Set(prev);
  newSet.delete(leadId);
  return newSet;
});
```

### **Visual Feedback**

#### **Status Badge with Loading:**
```jsx
<span className="inline-flex items-center px-2 py-1 text-xs font-semibold text-white">
  {isUpdating && (
    <Loader2 size={12} className="animate-spin mr-1" />
  )}
  {row.status || "Pending"}
</span>
```

#### **Table Row Loading State:**
```jsx
<tr className={`
  ${baseClasses}
  ${isUpdating ? "opacity-60 pointer-events-none" : ""}
`}>
```

### **Error Handling**

#### **API Error Types:**
```javascript
// 404 Not Found
"Lead not found. It may have been deleted."

// 403 Forbidden
"You do not have permission to update this lead."

// 400 Bad Request
"Invalid status value."

// 500+ Server Error
"Server error. Please try again later."

// Generic Error
"Failed to update lead status."
```

#### **User Feedback:**
- **Toast Notifications**: Success/error messages
- **Modal Error Display**: Error message within modal
- **Optimistic Rollback**: Automatic revert on failure
- **Loading States**: Visual indicators during processing

### **Accessibility Features**

#### **Keyboard Navigation:**
- **Arrow Keys**: Navigate between status options
- **Enter**: Confirm status update
- **Escape**: Close modal
- **Tab**: Navigate through interactive elements

#### **ARIA Support:**
```jsx
<button
  aria-pressed={selectedStatus === option.value}
  aria-describedby={`status-${option.value}-desc`}
>
  <div id={`status-${option.value}-desc`}>
    {option.description}
  </div>
</button>
```

#### **Screen Reader Support:**
- Proper ARIA labels and descriptions
- Status announcements on changes
- Clear button states and purposes

## Usage Instructions

### **For Users:**

#### **Update Lead Status:**
1. Navigate to Leads/Hitlist page
2. Click the actions dropdown (⋮) for any lead
3. Select a status option (Set as Pending, Warm, Hot, or Cold)
4. Status update modal opens showing current lead info
5. Select new status using mouse or arrow keys
6. Click "Update Status" or press Enter
7. Status updates immediately with loading indicator
8. Success message appears and modal closes

#### **Keyboard Navigation:**
- **↑↓**: Navigate between status options
- **Enter**: Confirm selection
- **Escape**: Cancel and close modal

### **For Developers:**

#### **Adding Status Updates to Other Components:**
```jsx
// 1. Import required components
import StatusUpdateModal from "../components/modals/StatusUpdateModal";
import { leadsService } from "../services/leadsService";

// 2. Add state management
const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
const [selectedLead, setSelectedLead] = useState(null);
const [updatingLeadIds, setUpdatingLeadIds] = useState(new Set());

// 3. Add status update handler
const handleStatusUpdate = async (leadId, newStatus) => {
  // Optimistic update logic
  // API call
  // Error handling with rollback
};

// 4. Add modal to JSX
<StatusUpdateModal
  isOpen={isStatusModalOpen}
  onClose={() => setIsStatusModalOpen(false)}
  lead={selectedLead}
  onStatusUpdate={handleStatusUpdate}
/>
```

#### **Customizing Status Options:**
```javascript
// In StatusUpdateModal.jsx
const statusOptions = [
  {
    value: 'CustomStatus',
    label: 'Custom Status',
    color: '#custom-color',
    bgColor: 'bg-custom-100 hover:bg-custom-200',
    textColor: 'text-custom-800',
    borderColor: 'border-custom-300',
    description: 'Custom status description'
  },
  // ... other options
];
```

## Testing

### **Manual Testing Checklist:**
- [ ] Status actions appear in DataTable dropdown
- [ ] Modal opens with correct lead information
- [ ] Status options are selectable and show descriptions
- [ ] Keyboard navigation works (arrows, Enter, Escape)
- [ ] Optimistic updates work immediately
- [ ] Loading indicators appear during update
- [ ] Success toast appears on successful update
- [ ] Error handling works for network failures
- [ ] Rollback works when API call fails
- [ ] Modal closes automatically on success

### **Test Scenarios:**
1. **Successful Update**: Normal status change flow
2. **Network Error**: Test with disconnected network
3. **Server Error**: Test with 500 response
4. **Permission Error**: Test with 403 response
5. **Concurrent Updates**: Multiple rapid status changes
6. **Keyboard Only**: Navigate and update using only keyboard

## Performance Considerations

### **Optimizations:**
- **Optimistic Updates**: Immediate UI response
- **Efficient State Updates**: Minimal re-renders
- **Loading States**: Clear user feedback
- **Error Recovery**: Automatic rollback on failure

### **Limitations:**
- **Concurrent Updates**: Last update wins
- **Network Dependency**: Requires stable connection
- **State Consistency**: Relies on optimistic updates

## Future Enhancements

### **Planned Features:**
1. **Bulk Status Updates**: Update multiple leads at once
2. **Status History**: Track status change history
3. **Custom Statuses**: User-defined status options
4. **Status Rules**: Automated status transitions
5. **Status Analytics**: Status change reporting

### **Technical Improvements:**
1. **Real-time Updates**: WebSocket status synchronization
2. **Offline Support**: Queue updates when offline
3. **Conflict Resolution**: Handle concurrent updates
4. **Audit Trail**: Log all status changes

## Support

For technical support or feature requests regarding the status update functionality, please:
1. Check the console for detailed error logs
2. Verify API connectivity and permissions
3. Test with different status values
4. Contact the development team with specific error messages

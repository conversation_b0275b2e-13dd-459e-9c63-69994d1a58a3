import { useAuth } from "../contexts/AuthContext";
import { useApp } from "../contexts/AppContext";
import { useNavigate, useLocation } from "react-router-dom";
import { useState, useRef, useEffect } from "react";
import { getPageTitleFromRoute } from "../config/menuItems";
import logoSmall from "../assets/images/logo.png";
import sampleProfile from "../assets/images/sample_profile.png";
import { Bell } from "lucide-react";
import {
  MdDarkMode,
  MdLightMode,
  MdLogout,
  MdSettings,
  MdPerson,
  MdComputer,
} from "react-icons/md";
import { GiPadlock } from "react-icons/gi";

function Navbar() {
  const { logout } = useAuth();
  const {
    toggleSidebar,
    darkMode,
    toggleDarkMode,
    themePreference,
    setSystemTheme,
    sidebarCollapsed,
    isMobile,
    sidebarOpen,
  } = useApp();
  const navigate = useNavigate();
  const location = useLocation();
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsProfileDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
  };

  return (
    <header className="bg-white dark:bg-black h-20 md:h-24 flex items-center justify-between pl-2 md:px-6 flex-shrink-0 pr-8 transition-colors duration-200">
      <div className="flex items-center md:pl-4">
        <button
          onClick={toggleSidebar}
          className="mr-4 p-2 rounded-lg transition-colors duration-200"
        >
          <div className="flex items-center gap-[2rem] md:gap-0">
            <div className="h-[2.7rem] block md:hidden">
              <img src={logoSmall} alt="" className="w-auto h-full" />
            </div>
            <div
              className={`hamburger ${
                (isMobile && sidebarOpen) || (!isMobile && sidebarCollapsed)
                  ? "is-active"
                  : ""
              }`}
            >
              <span className="line "></span>
              <span className="line"></span>
              <span className="line"></span>
            </div>
          </div>
        </button>
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white hidden md:block">
          {getPageTitleFromRoute(location.pathname)}
          {/* {location.pathname} */}
        </h1>
      </div>

      <div className="flex items-center space-x-4">
        <button className="size-11 rounded-full border border-gray-200 dark:border-gray-600 cursor-pointer grid place-items-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
          <Bell size={20} />
        </button>
        {/* <button
          onClick={toggleDarkMode}
          className="size-11 rounded-full border border-gray-200 dark:border-gray-600 cursor-pointer grid place-items-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
        >
          {darkMode ? (
            <MdLightMode
              size={20}
              className="text-gray-600 dark:text-gray-300"
            />
          ) : (
            <MdDarkMode
              size={20}
              className="text-gray-600 dark:text-gray-300"
            />
          )}
        </button> */}

        {/* Profile Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={toggleProfileDropdown}
            className="flex items-center rounded-full   w-fit text-green-700 dark:text-green-300 transition-colors duration-200 cursor-pointer"
          >
            <img
              src={sampleProfile}
              alt="Profile"
              className="size-11 rounded-full object-cover"
            />
            <p className="whitespace-nowrap mx-4 hidden md:block">
              Hello,{" "}
              <span className="font-semibold">
                {JSON.parse(localStorage.getItem("logged_in_user"))?.name ||
                  "Hildah"}
              </span>
            </p>
          </button>

          {/* Dropdown Menu */}
          {isProfileDropdownOpen && (
            <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-black border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
              <div className="py-2">
                {/* Profile Section */}
                <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center">
                    <img
                      src={sampleProfile}
                      alt="Profile"
                      className="size-10 rounded-full object-cover mr-3"
                    />
                    <div className="">
                      <p className="text-sm font-semibold text-gray-900 dark:text-white">
                        {JSON.parse(localStorage.getItem("logged_in_user"))
                          ?.name || "Hildah"}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Administrator
                      </p>
                    </div>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="py-1">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                    <MdPerson size={16} className="mr-3" />
                    Profile
                  </button>
                  <button
                    onClick={() => {
                      navigate("/change-password");
                      setIsProfileDropdownOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
                  >
                    <GiPadlock size={16} className="mr-3" />
                    Change password
                  </button>
                </div>

                {/* Logout */}
                <div className="border-t border-gray-200 dark:border-gray-700 py-1">
                  <button
                    onClick={() => {
                      handleLogout();
                      setIsProfileDropdownOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                  >
                    <MdLogout size={16} className="mr-3" />
                    Logout
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}

export default Navbar;

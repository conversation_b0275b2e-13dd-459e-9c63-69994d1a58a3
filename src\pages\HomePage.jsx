import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import PublicLayout from "../components/layouts/PublicLayout";

function HomePage() {
  const navigate = useNavigate();
  const { isLoggedIn, login, logout } = useAuth();

  const handleLogin = () => {
    login();
    navigate("/dashboard");
  };

  // If already logged in, show a different view
  if (isLoggedIn) {
    return (
      <PublicLayout>
        <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded-lg shadow-md">
          <h1 className="text-2xl font-bold text-center mb-6">Welcome Back!</h1>
          <p className="text-center text-gray-600 mb-6">
            You are already logged in.
          </p>
          <button
            onClick={() => navigate("/dashboard")}
            className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 mb-3"
          >
            Go to Dashboard
          </button>
          <button
            onClick={logout}
            className="w-full bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600"
          >
            Logout
          </button>
        </div>
      </PublicLayout>
    );
  }

  return (
    <PublicLayout>
      <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center mb-6">
          Welcome to Kingdom Bank
        </h1>
        <p className="text-center text-gray-600 mb-6">
          Please login to access your dashboard
        </p>
        <button
          onClick={handleLogin}
          className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
        >
          Login (Demo)
        </button>
      </div>
    </PublicLayout>
  );
}

export default HomePage;

import { BsQuestionCircle } from "react-icons/bs";
import { FaRegRectangleList } from "react-icons/fa6";
import { FaMoneyCheckAlt } from "react-icons/fa";
import {
  MdDashboard,
  MdPeople,
  MdAnchor,
  MdGroups,
  MdSettings,
  MdSupportAgent,
  MdTrackChanges,
  MdPersonOutline,
} from "react-icons/md";

// Centralized menu items configuration for both desktop and mobile sidebars
export const menuItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: MdDashboard,
    href: "/dashboard",
    activePattern: "^/dashboard$",
  },
  {
    id: "items",
    label: "Items",
    icon: FaRegRectangleList,
    href: "#",
    hasChildren: true,
    children: [
      { id: "products", label: "Products", href: "/items/products" },
      { id: "categories", label: "Categories", href: "/items/categories" },
      { id: "segments", label: "Segments", href: "/items/segments" },
      { id: "types", label: "Types", href: "/items/types" },
      { id: "sectors", label: "ISIC Sectors", href: "/items/sectors" },
      { id: "regions", label: "Regions", href: "/items/regions" },
      { id: "branches", label: "Branches", href: "/items/branches" },
      { id: "purpose", label: "Purposes", href: "/items/purposes" },
      { id: "purpose-categories", label: "Purpose Categories", href: "/items/purpose-categories" },
    ],
  },
  {
    id: "leads",
    label: "Leads",
    icon: MdPeople,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Leads - Hit List", href: "/leads/hitlist" },
      { id: "calls", label: "Calls", href: "/leads/calls" },
      { id: "visits", label: "Visits", href: "/leads/visits" },
      { id: "follow-ups", label: "Follow Ups", href: "/leads/follow-ups" },

    ],
  },

  {
    id: "customer-service",
    label: "Customer Service",
    icon: MdSupportAgent,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Hitlist", href: "/customer-service/hitlist", activePattern: "^/customer-service/hitlist$" },
      { id: "all-hitlist-records", label: "All", href: "/customer-service/hitlist/all", activePattern: "^/customer-service/hitlist/all$" },
      { id: "calls-to-do", label: "Calls To Do", href: "/customer-service/calls-to-do", activePattern: "^/customer-service/calls-to-do" }
    ],
  },

  {
    id: "loan-activities",
    label: "Loan Activities",
    icon: FaMoneyCheckAlt,
    href: "#",
    hasChildren: true,
    children: [
      { id: "hitlist", label: "Loan - Hit List", href: "/loan/hitlist" },
      { id: "calls", label: "Calls", href: "/loan/calls" },
      { id: "follow-ups", label: "Follow Ups", href: "/loan/follow-ups" },

    ],
  },
  {
    id: "anchors",
    label: "Anchors",
    icon: MdAnchor,
    href: "/anchors",
    activePattern: "^/anchors",
  },
  {
    id: "customers",
    label: "Customers",
    icon: MdGroups,
    href: "/customers",
  },
  {
    id: "my-targets",
    label: "My Targets",
    icon: MdTrackChanges,
    href: "/my-targets",
    activePattern: "^/my-targets$",
  },
  {
    id: "administration",
    label: "Administration",
    icon: MdSettings,
    href: "",
    hasChildren: true,
    children: [
      { id: "users", label: "Users", href: "/admin/users", activePattern: "^/admin/users" },
      { id: "roles", label: "Roles", href: "/admin/roles", activePattern: "^/admin/roles" },
      {
        id: "targets",
        label: "Targets",
        href: "#",
        hasChildren: true,
        children: [
          {
            id: "role-targets",
            label: "Role Targets",
            href: "/admin/targets/role-targets",
            activePattern: "^/admin/targets/role-targets"
          },
          {
            id: "individual-targets",
            label: "Individual Targets",
            href: "/admin/targets/individual-targets",
            activePattern: "^/admin/targets/individual-targets"
          },
        ]
      },
      // { id: "permissions", label: "Permissions", href: "#" },
    ],
  },
  {
    id: "support-tickets",
    label: "Support Tickets",
    icon: BsQuestionCircle,
    href: "/support-tickets",
    activePattern: "^/support-tickets",
  },
];

// Helper function to check if a route is active using regex patterns
export const isRouteActive = (pathname, item) => {
  // If item has activePattern, use regex matching
  if (item.activePattern) {
    const regex = new RegExp(item.activePattern);
    return regex.test(pathname);
  }

  // Fallback to exact match
  return item.href === pathname;
};

// Helper function to get page title from route (supports nested children)
export const getPageTitleFromRoute = (pathname) => {
  // First check direct matches
  const directMatch = menuItems.find(item => item.href === pathname);
  if (directMatch) {
    return directMatch.label;
  }

  // Check children for matches (supports nested children)
  for (const item of menuItems) {
    if (item.children) {
      const childMatch = item.children.find(child => child.href === pathname);
      if (childMatch) {
        return childMatch.label;
      }

      // Check nested children
      for (const child of item.children) {
        if (child.children) {
          const grandchildMatch = child.children.find(grandchild => grandchild.href === pathname);
          if (grandchildMatch) {
            return grandchildMatch.label;
          }
        }
      }
    }
  }

  // Check for role configuration pages
  if (pathname === "/admin/roles/create") {
    return "Create Role";
  }
  if (pathname.startsWith("/admin/roles/edit/")) {
    return "Edit Role";
  }

  // Check for targets pages
  if (pathname === "/admin/targets") {
    return "Targets";
  }
  if (pathname === "/admin/targets/role-targets") {
    return "Role Targets";
  }
  if (pathname === "/admin/targets/individual-targets") {
    return "Individual Targets";
  }

  // Check for hitlist page
  if (pathname === "/hitlist") {
    return "Hit list";
  }

  // Check for my targets page
  if (pathname === "/my-targets") {
    return "My Targets";
  }

  // Check for customer service pages
  if (pathname === "/customer-service/hitlist") {
    return "Customer Service - Hitlist";
  }
  if (pathname.startsWith("/customer-service/hitlist/")) {
    const hitlistCode = pathname.split("/").pop();
    return `Hitlist Details - ${hitlistCode}`;
  }
  if (pathname === "/customer-service/calls-to-do") {
    return "Customer Service - Calls To Do";
  }

  // Check for loan activity pages
  if (pathname === "/loan/hitlist") {
    return "Loan - Hit List";
  }
  if (pathname === "/loan/calls") {
    return "Loan Calls";
  }
  if (pathname === "/loan/follow-ups") {
    return "Loan Follow-ups";
  }

  // Check for login page
  if (pathname === "/login") {
    return "Login";
  }
  if (pathname === "/change-password") {
    return "Change Password";
  }

  // Default fallback
  return "Dashboard";
};

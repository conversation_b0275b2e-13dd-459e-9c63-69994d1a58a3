import instance from '../axios/instance.jsx';

// Anchors API endpoints
const ENDPOINTS = {
  ANCHORS: '/anchors',
  ANCHOR_BY_ID: (id) => `/anchors/${id}`,
  ANCHORS_STATISTICS: '/anchors/statistics',
};

// Anchors Service
export const anchorsService = {
  // Get all anchors with pagination support
  getAll: async (params = {}) => {
    try {
      console.log('=== FETCHING ALL ANCHORS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.ANCHORS}`);
      console.log('Query params:', params);
      
      const config = {};
      if (Object.keys(params).length > 0) {
        config.params = params;
      }
      
      const response = await instance.get(ENDPOINTS.ANCHORS, config);
      
      console.log('Anchors response:', response.data);
      console.log('Total anchors:', response.data.meta?.total || response.data.length);
      console.log('===============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching anchors:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Anchors endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view anchors.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching anchors. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch anchors.');
      }
    }
  },

  // Create a new anchor
  create: async (anchorData) => {
    try {
      console.log('=== CREATING NEW ANCHOR ===');
      console.log('Anchor data:', anchorData);
      console.log(`API Endpoint: POST ${ENDPOINTS.ANCHORS}`);
      
      const response = await instance.post(ENDPOINTS.ANCHORS, anchorData);
      
      console.log('Anchor creation response:', response.data);
      console.log('==============================');
      
      return response.data;
    } catch (error) {
      console.error('Error creating anchor:', error);
      
      // Handle different error types
      if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid anchor data provided.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to create anchors.');
      } else if (error.response?.status === 409) {
        throw new Error('An anchor with this email already exists.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while creating anchor. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to create anchor.');
      }
    }
  },

  // Update an existing anchor
  update: async (id, anchorData) => {
    try {
      console.log('=== UPDATING ANCHOR ===');
      console.log(`Anchor ID: ${id}`);
      console.log('Updated data:', anchorData);
      console.log(`API Endpoint: PATCH ${ENDPOINTS.ANCHOR_BY_ID(id)}`);
      
      const response = await instance.patch(ENDPOINTS.ANCHOR_BY_ID(id), anchorData);
      
      console.log('Anchor update response:', response.data);
      console.log('========================');
      
      return response.data;
    } catch (error) {
      console.error('Error updating anchor:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Anchor not found. It may have been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to update this anchor.');
      } else if (error.response?.status === 400) {
        throw new Error(error.response?.data?.message || 'Invalid anchor data provided.');
      } else if (error.response?.status === 409) {
        throw new Error('An anchor with this email already exists.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while updating anchor. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to update anchor.');
      }
    }
  },

  // Delete an anchor
  delete: async (id) => {
    try {
      console.log('=== DELETING ANCHOR ===');
      console.log(`Anchor ID: ${id}`);
      console.log(`API Endpoint: DELETE ${ENDPOINTS.ANCHOR_BY_ID(id)}`);
      
      const response = await instance.delete(ENDPOINTS.ANCHOR_BY_ID(id));
      
      console.log('Anchor deletion response:', response.status);
      console.log('========================');
      
      return response.status === 204 || response.status === 200;
    } catch (error) {
      console.error('Error deleting anchor:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Anchor not found. It may have already been deleted.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to delete this anchor.');
      } else if (error.response?.status === 409) {
        throw new Error('Cannot delete anchor. It is currently being used by leads.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while deleting anchor. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to delete anchor.');
      }
    }
  },

  // Get a single anchor by ID
  getById: async (id) => {
    try {
      console.log('=== FETCHING ANCHOR BY ID ===');
      console.log(`Anchor ID: ${id}`);
      console.log(`API Endpoint: GET ${ENDPOINTS.ANCHOR_BY_ID(id)}`);
      
      const response = await instance.get(ENDPOINTS.ANCHOR_BY_ID(id));
      
      console.log('Anchor details response:', response.data);
      console.log('==============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching anchor by ID:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Anchor not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view this anchor.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching anchor. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch anchor details.');
      }
    }
  },

  // Get anchors statistics
  getStatistics: async () => {
    try {
      console.log('=== FETCHING ANCHORS STATISTICS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.ANCHORS_STATISTICS}`);
      
      const response = await instance.get(ENDPOINTS.ANCHORS_STATISTICS);
      
      console.log('Anchors statistics response:', response.data);
      console.log('====================================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching anchors statistics:', error);
      
      // Handle different error types
      if (error.response?.status === 404) {
        throw new Error('Statistics endpoint not found.');
      } else if (error.response?.status === 403) {
        throw new Error('You do not have permission to view statistics.');
      } else if (error.response?.status >= 500) {
        throw new Error('Server error while fetching statistics. Please try again later.');
      } else {
        throw new Error(error.response?.data?.message || 'Failed to fetch statistics.');
      }
    }
  },
};

/**
 * Format anchors data for table display
 * @param {Object} apiResponse - Response from anchors API
 * @returns {Array} - Formatted data for DataTable
 */
export const formatAnchorsForTable = (apiResponse) => {
  if (!apiResponse) {
    return [];
  }

  // Handle both paginated and direct array responses
  const anchorsData = apiResponse.data || apiResponse;
  
  if (!Array.isArray(anchorsData)) {
    console.warn('Invalid anchors data format:', apiResponse);
    return [];
  }

  return anchorsData.map((anchor, index) => ({
    id: anchor.id || `anchor${index + 1}`,
    anchorID: anchor.id || `ANC${index + 1}`,
    name: anchor.name || "Unknown Anchor",
    accountId: anchor.account_id || "N/A",
    email: anchor.email || "No email",
    phoneNumber: anchor.phone_number || "No phone",
    leadsCount: anchor.leads_count || 0,
    isInUse: anchor.is_in_use || false,
    createdAt: anchor.created_at,
    // Additional fields for compatibility
    type: "Business", // Default type since not in API response
    location: "Unknown", // Default location since not in API response
    mobile: anchor.phone_number || "No phone",
  }));
};

/**
 * Format anchors for dropdown selection (React Select)
 * @param {Object} apiResponse - Response from anchors API
 * @returns {Array} - Formatted options for Select component
 */
export const formatAnchorsForDropdown = (apiResponse) => {
  if (!apiResponse) {
    return [];
  }

  // Handle both paginated and direct array responses
  const anchorsData = apiResponse.data || apiResponse;
  
  if (!Array.isArray(anchorsData)) {
    console.warn('Invalid anchors data format:', apiResponse);
    return [];
  }

  return anchorsData.map((anchor) => {
    // Create label with format: {anchor_name} - {account_id}
    const accountId = anchor.account_id || "No Account ID";
    const label = `${anchor.name} - ${accountId}`;

    return {
      value: anchor.id,
      label: label,
      name: anchor.name, // Keep original name for reference
      accountId: accountId, // Keep account ID for reference
      email: anchor.email,
      phoneNumber: anchor.phone_number,
      leadsCount: anchor.leads_count || 0,
      isInUse: anchor.is_in_use || false,
    };
  });
};

/**
 * Format anchor date for display
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date
 */
export const formatAnchorDate = (dateString) => {
  if (!dateString) return "Unknown date";
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (error) {
    console.warn('Error formatting anchor date:', dateString);
    return "Invalid date";
  }
};

/**
 * Get usage status color for anchors
 * @param {boolean} isInUse - Whether anchor is in use
 * @param {number} leadsCount - Number of leads for this anchor
 * @returns {string} - Color classes
 */
export const getAnchorUsageColor = (isInUse, leadsCount = 0) => {
  if (!isInUse || leadsCount === 0) {
    return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
  } else if (leadsCount >= 20) {
    return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
  } else if (leadsCount >= 10) {
    return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
  } else {
    return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
  }
};

export default anchorsService;

import instance from '../axios/instance.jsx';

// Employers API endpoints
const ENDPOINTS = {
  EMPLOYERS: '/employers',
  EMPLOYER_BY_ID: (id) => `/employers/${id}`,
};

// Employers Service
export const employersService = {
  // Get all employers
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.EMPLOYERS);
      return response.data;
    } catch (error) {
      console.error('Error fetching employers:', error);
      throw error;
    }
  },

  // Create a new employer
  create: async (employerData) => {
    try {
      const response = await instance.post(ENDPOINTS.EMPLOYERS, employerData);
      return response.data;
    } catch (error) {
      console.error('Error creating employer:', error);
      throw error;
    }
  },

  // Update an existing employer
  update: async (id, employerData) => {
    try {
      const response = await instance.patch(ENDPOINTS.EMPLOYER_BY_ID(id), employerData);
      return response.data;
    } catch (error) {
      console.error('Error updating employer:', error);
      throw error;
    }
  },

  // Delete an employer
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.EMPLOYER_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting employer:', error);
      throw error;
    }
  },

  // Get a single employer by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.EMPLOYER_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching employer by ID:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatEmployerData = (employer) => {
  return {
    id: employer.id,
    name: employer.name,
    industry: employer.industry || 'N/A',
    location: employer.location || 'N/A',
    contactPerson: employer.contactPerson || 'N/A',
    phoneNumber: employer.phoneNumber || 'N/A',
    email: employer.email || 'N/A',
    addedOnDate: employer.addedOnDate,
    addedBy: employer.addedBy || 'N/A',
    // Format date for display
    formattedDate: formatDateTime(employer.addedOnDate),
  };
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatEmployersResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatEmployerData);
};

export default employersService;

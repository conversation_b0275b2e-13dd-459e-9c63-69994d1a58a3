import React, { useState, useEffect } from 'react';
import Chart from 'react-apexcharts';
import { UserGroupIcon, PhoneIcon, CalendarIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

/*
  SummaryCard Component: displays a KPI with a title, value, and icon.
  Props:
    - title: label of the metric
    - value: numeric value to display
    - icon: SVG icon component
    - iconColor: Tailwind text color for the icon
    - bgColor: Tailwind background color for the card
*/
const SummaryCard = ({ title, value, icon: Icon, iconColor, bgColor }) => (
  <div className={`flex items-center p-4 rounded-lg shadow ${bgColor} min-h-[7rem]`}>
    <div className={`p-2 rounded-full ${iconColor} bg-grey bg-opacity-30`}>
      <Icon className="w-6 h-6" /> {/* slightly increased icon size for balance */}
    </div>
    <div className="ml-4">
      <p className="text-sm font-medium text-gray-600">{title}</p>
      <p className="mt-1 text-xl font-semibold text-gray-900">{value}</p>
    </div>
  </div>
);


/*
  TaskListCard Component: displays a list of tasks (upcoming or overdue activities).
  Props:
    - title: title of the list
    - tasks: array of task objects { id, type, customer, date }
*/
const TaskListCard = ({ title, tasks }) => (
  <div className="p-4 rounded-lg shadow bg-white">
    <h2 className="text-lg font-medium text-gray-700 mb-2">{title}</h2>
    <ul className="divide-y divide-gray-200">
      {tasks.map(task => (
        <li key={task.id} className="py-2">
          <div className="text-sm text-gray-500">{task.type} - {task.customer}</div>
          <div className="text-sm font-semibold text-gray-800">{task.date}</div>
        </li>
      ))}
    </ul>
  </div>
);

/*
  Dashboard Component: main component assembling the summary cards, charts, and lists.
*/
const BankerDashboard = () => {
  // Mock data (to be replaced by API calls)
  const [data, setData] = useState({
    hitlistSize: 120,
    calls: { made: 75, target: 100 },
    visits: { made: 60, target: 80 },
    contacted: 90, // number of customers on hitlist contacted
    upcoming: [
      { id: 1, type: 'Call', customer: 'ABC Corp', date: '2025-07-24 10:00' },
      { id: 2, type: 'Visit', customer: 'XYZ Ltd', date: '2025-07-25 14:00' },
      { id: 3, type: 'Call', customer: 'Foo Industries', date: '2025-07-26 09:30' },
    ],
    overdue: [
      { id: 1, type: 'Call', customer: 'Bar Co', date: '2025-07-10 11:00' },
      { id: 2, type: 'Visit', customer: 'Baz LLC', date: '2025-07-12 15:30' },
    ],
  });

  // Chart configuration for Calls vs Target (bar chart)
  const callsChart = {
    series: [
      { name: 'Made', data: [data.calls.made] },
      { name: 'Target', data: [data.calls.target] },
    ],
    options: {
      chart: { type: 'bar', toolbar: { show: false } },
      plotOptions: { bar: { horizontal: false, columnWidth: '50%' } },
      xaxis: { categories: ['Calls MTD'] },
      colors: ['#69AF57', '#1C5B41'], 
      legend: { position: 'top' },
      yaxis: { title: { text: 'Number' } },
    }
  };

  // Chart configuration for Visits vs Target (bar chart)
  const visitsChart = {
    series: [
      { name: 'Made', data: [data.visits.made] },
      { name: 'Target', data: [data.visits.target] },
    ],
    options: {
      chart: { type: 'bar', toolbar: { show: false } },
      plotOptions: { bar: { horizontal: false, columnWidth: '50%' } },
      xaxis: { categories: ['Visits MTD'] },
      colors: ['#69AF57', '#1C5B41'], 
      legend: { position: 'top' },
      yaxis: { title: { text: 'Number' } },
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Summary Widgets */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <SummaryCard 
          title="Hitlist Size" 
          value={data.hitlistSize} 
          icon={UserGroupIcon} 
          iconColor="text-[#69AF57]" 
          bgColor="bg-white" 
        />
        <SummaryCard 
          title="Calls Made (MTD)" 
          value={`${data.calls.made} / ${data.calls.target}`} 
          icon={PhoneIcon} 
          iconColor="text-[#1C5B41]" 
          bgColor="bg-white" 
        />
        <SummaryCard 
          title="Visits Made (MTD)" 
          value={`${data.visits.made} / ${data.visits.target}`} 
          icon={CalendarIcon} 
          iconColor="text-[#69AF57]" 
          bgColor="bg-white" 
        />
        <SummaryCard 
          title="Hitlist Contacted" 
          value={data.contacted} 
          icon={CheckCircleIcon} 
          iconColor="text-[#1C5B41]" 
          bgColor="bg-white" 
        />
      </div>

      {/* Charts: Calls vs Target and Visits vs Target */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="p-4 rounded-lg shadow bg-white">
          <h2 className="text-lg font-medium text-gray-700 mb-2">Calls Made vs Target</h2>
          <Chart 
            options={callsChart.options} 
            series={callsChart.series} 
            type="bar" 
            height={300} 
          />
        </div>
        <div className="p-4 rounded-lg shadow bg-white">
          <h2 className="text-lg font-medium text-gray-700 mb-2">Visits Made vs Target</h2>
          <Chart 
            options={visitsChart.options} 
            series={visitsChart.series} 
            type="bar" 
            height={300} 
          />
        </div>
      </div>

      {/* Upcoming and Overdue Activities */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <TaskListCard title="Upcoming Calls & Visits" tasks={data.upcoming} />
        <TaskListCard title="Overdue Calls & Visits" tasks={data.overdue} />
      </div>
    </div>
  );
}

/*
  Note: 
  - Mock data is hardcoded in the state; replace `useEffect` and `fetch` logic here for real API data as needed.
  - The layout is mobile-responsive: widgets and lists stack on small screens and form columns on larger screens, as per Tailwind’s responsive design.
*/

export default BankerDashboard;
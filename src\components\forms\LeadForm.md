# Lead Form Component

A modern, responsive lead generation form with conditional field behavior and comprehensive validation.

## Features

- **Single-column layout** with clean sectioned design
- **Conditional field rendering** based on customer category and lead type
- **Real-time validation** with visual feedback
- **Responsive design** with max-height and scroll support
- **Tooltips** for complex fields (ISIC Sector, Client ID, RM Code)
- **Auto-filled fields** for RM Code and RM Name

## Form Sections

### 1. Lead Information
- **Customer Name** (required)
- **Phone Number** (required, validated for Kenyan format)
- **Customer Category** (dropdown with predefined options)
- **ISIC Sector** (searchable dropdown with industry classifications)
- **Lead Type** (New/Existing - drives conditional logic)
- **Client ID** (only visible if "Existing" is selected)

### 2. Branch & Relationship Assignment
- **Branch Name** (required dropdown)
- **RM Code** (auto-filled, read-only)
- **RM Name** (auto-filled, read-only)

### 3. Contact Person Details (Conditional)
- **Contact Person Name** (required for non-Employed, non-Youth categories)
- **Contact Person Phone No** (optional)

### 4. Employer Information (Conditional)
- **Employer Name** (required dropdown for Employed customers)

## Conditional Logic

### Client ID Field
- **Shows when**: Lead Type is "Existing"
- **Required**: Yes (when visible)

### Contact Person Section
- **Shows when**: Customer Category is NOT "Employed" or "Junior and youth"
- **Required**: Contact Person Name is required when section is visible

### Employer Information Section
- **Shows when**: Customer Category is "Employed"
- **Required**: Employer Name is required when section is visible

## Validation Rules

### Required Fields
- Customer Name
- Phone Number (with Kenyan format validation)
- Customer Category
- ISIC Sector
- Branch Name

### Conditional Required Fields
- Client ID (when Lead Type is "Existing")
- Contact Person Name (when category requires contact person)
- Employer Name (when category is "Employed")

### Phone Number Validation
- Accepts formats: `+************` or `0712345678`
- Validates Kenyan mobile number patterns

## Form Actions

### Submit Button
- **Create Mode**: "Create Lead"
- **Edit Mode**: "Update Lead"
- **Loading State**: "Processing..." with spinner
- **Disabled**: When form is submitting or validation fails

### Reset Button
- Clears all form fields to initial state
- Resets validation errors

### Cancel Button
- Closes modal without saving changes

## Data Structure

The form outputs a structured object ready for API integration:

```javascript
{
  customerName: 'Jane Doe',
  phoneNumber: '+************',
  customerCategory: 'Employed',
  isicSector: 'Financial and insurance activities',
  leadType: 'New',
  clientId: '', // Only for existing customers
  branchName: 'Westlands Branch',
  rmCode: 'RM001', // Auto-filled
  rmName: 'Current User', // Auto-filled
  contactPersonName: '', // Conditional
  contactPersonPhone: '', // Conditional
  employerName: 'Kenya Commercial Bank' // Conditional
}
```

## Integration Points

### API Endpoints (Ready for Implementation)
- **POST** `/api/leads` - Create new lead
- **PUT** `/api/leads/{id}` - Update existing lead
- **GET** `/api/branches` - Fetch branch list
- **GET** `/api/isic-sectors` - Fetch ISIC sectors
- **GET** `/api/employers` - Fetch employer list

### Authentication Context
- RM Code and RM Name should be populated from logged-in user context
- Replace mock values with actual user data

### Dynamic Data Sources
- Customer Categories: Static list (as per business requirements)
- ISIC Sectors: Should be fetched from API for latest classifications
- Branches: Should be fetched from API for current active branches
- Employers: Should be fetched from API for recognized employers

## Styling

- **Container**: `max-height: 80vh` with `overflow-y: auto`
- **Sections**: Clear visual separation with borders and spacing
- **Fields**: Consistent padding and responsive design
- **Validation**: Red borders and error messages for invalid fields
- **Tooltips**: Hover-activated info tooltips for complex fields

## Accessibility

- Proper label associations
- Keyboard navigation support
- Screen reader friendly
- Focus management
- Error announcements

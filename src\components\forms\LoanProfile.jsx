import { useState } from "react";
import { X, User, DollarSign, Calendar, FileText } from "lucide-react";

const LoanProfile = ({ item, onClose }) => {
  const [activeTab, setActiveTab] = useState("profile");

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return "Invalid Date";
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "disbursed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
    }
  };

  const ProfileTab = () => (
    <div className="p-6 space-y-6">
      {/* Customer Information */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <User className="w-5 h-5 mr-2" />
          Customer Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Customer Name
            </label>
            <p className="text-gray-900 dark:text-white font-medium">
              {item?.customerName || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Phone Number
            </label>
            <p className="text-gray-900 dark:text-white">
              {item?.phoneNumber || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Account Number
            </label>
            <p className="text-gray-900 dark:text-white">
              {item?.accountNumber || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Loan Officer
            </label>
            <p className="text-gray-900 dark:text-white">
              {item?.officer || "N/A"}
            </p>
          </div>
        </div>
      </div>

      {/* Loan Details */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2" />
          Loan Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Loan Amount
            </label>
            <p className="text-gray-900 dark:text-white font-semibold text-lg">
              {item?.loanAmount || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Loan Type
            </label>
            <p className="text-gray-900 dark:text-white">
              {item?.loanType || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Interest Rate
            </label>
            <p className="text-gray-900 dark:text-white">
              {item?.interestRate || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Status
            </label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item?.status)}`}>
              {item?.status || "Unknown"}
            </span>
          </div>
        </div>
      </div>

      {/* Important Dates */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          Important Dates
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Disbursement Date
            </label>
            <p className="text-gray-900 dark:text-white">
              {formatDate(item?.disbursementDate)}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Maturity Date
            </label>
            <p className="text-gray-900 dark:text-white">
              {formatDate(item?.maturityDate)}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Last Interaction
            </label>
            <p className="text-gray-900 dark:text-white">
              {item?.lastInteraction || "N/A"}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-600 dark:text-gray-400">
              Total Calls
            </label>
            <p className="text-gray-900 dark:text-white font-medium">
              {item?.calls || 0}
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const HistoryTab = () => (
    <div className="p-6">
      <div className="text-center py-8">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Loan History
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Loan history and activity timeline will be displayed here.
        </p>
      </div>
    </div>
  );

  const AttachmentsTab = () => (
    <div className="p-6">
      <div className="text-center py-8">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Loan Documents
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Loan documents and attachments will be displayed here.
        </p>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-[80vh]">
      {/* Fixed Header */}
      <div className="flex-shrink-0">
        {/* Profile Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <User className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">{item?.customerName || "Unknown Customer"}</h1>
                <p className="text-blue-100">Loan ID: {item?.id || "N/A"}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item?.status)}`}>
                    {item?.status || "Unknown"}
                  </span>
                  <span className="text-blue-100">
                    {item?.loanAmount || "N/A"}
                  </span>
                </div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: "profile", label: "Profile", icon: User },
              { id: "history", label: "History", icon: Calendar },
              { id: "attachments", label: "Documents", icon: FileText },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600 dark:text-blue-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Scrollable Body */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        {/* Tab Content */}
        {activeTab === "profile" && <ProfileTab />}
        {activeTab === "history" && <HistoryTab />}
        {activeTab === "attachments" && <AttachmentsTab />}
      </div>
    </div>
  );
};

export default LoanProfile;

# API Integration Documentation

## Overview
This document describes the API integration setup for the KB Tracker Frontend application, specifically for the Regions functionality.

## Setup

### 1. Environment Configuration
The API base URL is configured in the `.env` file:
```
VITE_API_BASE_URL=http://localhost:3000/api/v1
```

### 2. Dependencies Added
- `axios` - For HTTP requests
- `react-toastify` - For toast notifications

### 3. API Context
Created `src/contexts/ApiContext.jsx` which provides:
- Centralized API configuration
- Axios instance with interceptors
- Region-specific API methods
- Automatic error handling with toast notifications

### 4. Toast Integration
Added `ToastContainer` to `App.jsx` for displaying notifications.

## Usage

### In Components
```jsx
import { useApi } from "../contexts/ApiContext";

const MyComponent = () => {
  const { regionApi, branchApi, permissionsApi, rolesApi } = useApi();
  
  // Get all regions
  const fetchRegions = async () => {
    try {
      const data = await regionApi.getAll();
      setRegions(data);
    } catch (error) {
      // Error is automatically handled by the API context
    }
  };
  
  // Create a region
  const createRegion = async (regionData) => {
    try {
      const result = await regionApi.create(regionData);
      // Success toast is automatically shown
      return result;
    } catch (error) {
      // Error toast is automatically shown
    }
  };
};
```

## API Methods Available

### Region API (`regionApi`)
- `getAll()` - Fetch all regions
- `getById(id)` - Fetch region by ID
- `create(data)` - Create new region
- `update(id, data)` - Update existing region (uses PATCH method)
- `delete(id)` - Delete region

### Branch API (`branchApi`)
- `getAll()` - Fetch all branches
- `getById(id)` - Fetch branch by ID
- `create(data)` - Create new branch
- `update(id, data)` - Update existing branch (uses PATCH method)
- `delete(id)` - Delete branch

### Permissions API (`permissionsApi`)
- `getAll()` - Fetch all permissions (categories derived from description field)

### Roles API (`rolesApi`)
- `getAll()` - Fetch all roles
- `getById(id)` - Fetch role by ID
- `create(data)` - Create new role
- `update(id, data)` - Update existing role (uses PATCH method)
- `delete(id)` - Delete role

## Files Modified/Created

### Created:
- `.env` - Environment configuration
- `src/contexts/ApiContext.jsx` - API context provider
- `src/utils/apiService.js` - Utility service for API calls

### Modified:
- `src/App.jsx` - Added ApiProvider and ToastContainer
- `src/components/forms/RegionForm.jsx` - Integrated with API
- `src/pages/Regions.jsx` - Integrated with API for CRUD operations
- `src/components/forms/BranchForm.jsx` - Integrated with API and region dropdown
- `src/pages/Branches.jsx` - Integrated with API for CRUD operations
- `src/pages/RoleConfiguration.jsx` - Integrated with permissions and roles API
- `src/pages/Roles.jsx` - Integrated with roles API for listing and management
- `src/components/forms/UserForm.jsx` - Integrated with users API and dynamic dropdowns
- `src/services/userService.js` - Comprehensive user service with CRUD operations

## Error Handling
- Automatic error handling via axios interceptors
- Toast notifications for success/error messages
- Network error handling
- HTTP status code specific error messages
- Fallback to mock data when API is unavailable (development mode)
- Array validation to prevent filter errors in DataTable
- Graceful degradation when backend is not running

## Authentication
The API service is prepared for authentication:
- Token storage in localStorage
- Automatic token inclusion in requests
- Automatic redirect to login on 401 errors

## Best Practices Implemented
1. Environment variables for configuration
2. Centralized API management
3. Automatic error handling
4. Toast notifications for user feedback
5. Separation of concerns
6. Reusable API context
7. Proper loading states
8. Data refresh after CRUD operations

## Testing the Integration
1. Start the development server: `npm run dev`
2. Navigate to the Regions page
3. Try creating, editing, and deleting regions
4. Check browser console for API calls
5. Verify toast notifications appear

## Backend API Expected Format
The backend should provide the following endpoints:

**Regions:**
- `GET /api/v1/regions` - List all regions
- `GET /api/v1/regions/:id` - Get region by ID
- `POST /api/v1/regions` - Create new region
- `PATCH /api/v1/regions/:id` - Update region
- `DELETE /api/v1/regions/:id` - Delete region

**Branches:**
- `GET /api/v1/branches` - List all branches
- `GET /api/v1/branches/:id` - Get branch by ID
- `POST /api/v1/branches` - Create new branch
- `PATCH /api/v1/branches/:id` - Update branch
- `DELETE /api/v1/branches/:id` - Delete branch

**Permissions:**
- `GET /api/v1/permissions` - List all permissions (categories auto-generated from description field)

**Roles:**
- `GET /api/v1/roles` - List all roles
- `GET /api/v1/roles/:id` - Get role by ID
- `POST /api/v1/roles` - Create new role
- `PATCH /api/v1/roles/:id` - Update role
- `DELETE /api/v1/roles/:id` - Delete role

Backend API Response Format:
```json
{
  "data": [
    {
      "id": "d1519bad-1299-4644-8d6b-1d706367fcf9",
      "name": "NAIROBI",
      "branchCount": 0,
      "created_at": "2025-07-25T11:42:19.298Z",
      "updated_at": null
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

Frontend Mapped Data Structure:
```json
{
  "id": "string",
  "name": "string",
  "addedBy": "System",
  "addedOn": "25 Jul 2025",
  "branchCount": "number",
  "created_at": "ISO date string",
  "updated_at": "ISO date string"
}
```

## Date Formatting
All dates are formatted in the "25 Jul 2025" format using the `formatDateDisplay` utility function from `src/utils/dateUtils.js`.

## Role Configuration Integration ✅ WORKING

### Permission Categorization
The RoleConfiguration component automatically creates categories from the permission's description field:
- Permission with description "items" → Creates "items-management" category named "Items Management"
- Permission with description "administration" → Creates "administration-management" category named "Administration Management"
- Permission with description "leads" → Creates "leads-management" category named "Leads Management"
- All permissions with the same description are grouped into the same category

### Confirmed Working Features
✅ **API Integration** - Successfully connects to `/api/v1/permissions`
✅ **Response Parsing** - Handles `{data: [...], meta: {...}}` format
✅ **Dynamic Categorization** - Groups permissions by description field
✅ **UI Rendering** - Displays categories with permissions
✅ **Role CRUD** - Create, read, update, delete operations
✅ **Error Handling** - Graceful fallbacks and error messages

### Service Architecture
Following the pattern of `isicSectorsService.js`, the permissions system uses:
- `src/services/permissionsService.js` - Dedicated service for permissions and roles API calls
- Proper error handling and toast notifications
- Data formatting and transformation functions
- Icon and color mapping for categories

### Dynamic Permission Loading
- Fetches permissions from the database (single endpoint)
- Groups permissions by their description field automatically
- Creates categories dynamically from permission descriptions
- Loads existing role data for editing
- Handles loading states and error scenarios

### Actual Permission Data Structure
```json
{
  "data": [
    {
      "id": "customers.create",
      "name": "Create Customers",
      "description": "customers",
      "roleCount": 0
    },
    {
      "id": "items.create",
      "name": "Create Items",
      "description": "items",
      "roleCount": 0
    },
    {
      "id": "customers.view",
      "name": "View Customers",
      "description": "customers",
      "roleCount": 0
    },
    {
      "id": "items.view",
      "name": "View Items",
      "description": "items",
      "roleCount": 0
    }
  ],
  "meta": {
    "total": 8,
    "page": 1,
    "limit": 10,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  }
}
```

This creates two categories:
- **Customers Management** (from description "customers") with customer permissions
- **Items Management** (from description "items") with item permissions

### Permission ID Format
✅ **Already Correct** - Permission IDs are in `"resource.action"` format:
- `"customers.create"`, `"customers.view"`, `"customers.modify"`, `"customers.delete"`
- `"items.create"`, `"items.view"`, `"items.modify"`, `"items.delete"`

## Troubleshooting

### "data.filter is not a function" Error
This error occurs when the API returns data in an unexpected format. The integration includes several safeguards:

1. **API Context**: Handles different response formats (direct array, `data.data`, `data.regions`)
2. **Component Level**: Ensures regions state is always an array
3. **DataTable**: Validates data is an array before filtering
4. **Mock Fallback**: Returns mock data when API is unavailable

### Backend Not Running
When the backend API is not available, the application will:
- Display mock data for development
- Show toast notifications indicating mock mode
- Log warnings to console
- Continue functioning normally

### Network Issues
- Automatic retry logic can be added to the axios interceptors
- Toast notifications inform users of network problems
- Application remains functional with cached/mock data

## User Management Integration ✅ WORKING

### User Service Implementation
Created comprehensive `src/services/userService.js` with:
- **Full CRUD Operations** - Create, read, update, delete users
- **Dynamic Dropdowns** - Loads roles and branches from backend
- **Data Formatting** - Converts between frontend/backend formats
- **Validation** - Comprehensive form validation
- **Error Handling** - User-friendly error messages

### Backend Data Format
The service handles the expected backend format:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "password": "SecurePassword123!",
  "rm_code": "RM001",
  "role_id": "550e8400-e29b-41d4-a716-446655440000",
  "branch_id": "550e8400-e29b-41d4-a716-446655440001",
  "last_login": "2024-01-15T10:30:00.000Z"
}
```

### UserForm Enhancements
- **Added RM Code field** - Optional field for relationship manager code
- **Dynamic role/branch dropdowns** - Loaded from backend APIs
- **Backend integration** - Uses userService for all operations
- **Improved validation** - 8-character minimum password requirement
- **Loading states** - Shows loading indicators for dropdowns
- **Error handling** - Toast notifications for user feedback

### API Endpoints Used
- `GET /api/v1/users` - List all users
- `GET /api/v1/users/:id` - Get user by ID
- `POST /api/v1/users` - Create new user
- `PATCH /api/v1/users/:id` - Update user
- `DELETE /api/v1/users/:id` - Delete user
- `GET /api/v1/roles` - Get roles for dropdown
- `GET /api/v1/branches` - Get branches for dropdown

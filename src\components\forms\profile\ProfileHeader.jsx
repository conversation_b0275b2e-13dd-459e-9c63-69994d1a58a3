import { X } from "lucide-react";

const ProfileHeader = ({ item, onClose, getStatusColor }) => {
  return (
    <div className="flex items-center justify-between -mt-4 -mx-6 px-6 pt-6 pb-2 border-b border-gray-200">
      <div className="flex items-center gap-3">
        <h2 className="text-xl font-semibold" style={{ color: "#3d4465" }}>
          {item.name}
        </h2>
        <span
          className="px-3 py-1 text-xs font-semibold text-white rounded"
          style={{ backgroundColor: getStatusColor(item.status) }}
        >
          {item.status || "Pending"}
        </span>
      </div>
      <button
        onClick={onClose}
        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        aria-label="Close"
      >
        <X size={20} className="text-gray-500" />
      </button>
    </div>
  );
};

export default ProfileHeader;

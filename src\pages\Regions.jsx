import { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../components/layouts/PrivateLayout";
import DataTable from "../components/common/DataTable";
import DeleteConfirmation from "../components/forms/DeleteConfirmation";
import RegionForm from "../components/forms/RegionForm";
import { useApi } from "../contexts/ApiContext";

const Regions = () => {
  const { regionApi } = useApi();
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [regions, setRegions] = useState([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Define columns of the table here and what to render
  const columns = [
    {
      key: "name",
      title: "REGION",
      render: (value) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {value}
        </span>
      ),
    },
    {
      key: "branchCount",
      title: "<PERSON><PERSON><PERSON><PERSON>",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
          {value || 0}
        </span>
      ),
    },
    {
      key: "addedBy",
      title: "ADDED BY",
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
          {value}
        </span>
      ),
    },
    {
      key: "addedOn",
      title: "CREATED ON",
      render: (value) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {value}
        </span>
      ),
    },
  ];

  // Fetch regions data
  const fetchRegions = useCallback(async () => {
    setLoading(true);
    try {
      const data = await regionApi.getAll();
      // Ensure data is always an array
      setRegions(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching regions:", error);
      // Set empty array on error to prevent filter issues
      setRegions([]);
      // Error is handled by the API context interceptor
    } finally {
      setLoading(false);
    }
  }, [regionApi]);

  // Load regions on component mount and when refresh is triggered
  useEffect(() => {
    fetchRegions();
  }, [fetchRegions, refreshTrigger]);

  // Helper function to trigger refresh
  const refreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Form submission handlers
  const handleCreateSubmit = (result) => {
    console.log("Created region:", result);
    // Refresh the data after successful creation
    refreshData();
  };

  const handleEditSubmit = (result) => {
    console.log("Updated region:", result);
    // Refresh the data after successful update
    refreshData();
  };

  const handleDeleteConfirm = async (region) => {
    try {
      await regionApi.delete(region.id);
      console.log("Deleted region:", region);
      // Refresh the data after successful deletion
      refreshData();
    } catch (error) {
      console.error("Error deleting region:", error);
      // Error is handled by the API context interceptor
    }
  };

  const handleView = (region) => {
    console.log("View region:", region);
    // Here you would typically navigate to view page or show view modal
  };

  const handleLoadMore = () => {
    setLoadingMore(true);
    // Simulate API call
    setTimeout(() => {
      setLoadingMore(false);
      console.log("Load more regions");
    }, 2000);
  };


  return (
    <PrivateLayout>
      {/* Data Table */}
      <div className="">
        <DataTable
          columns={columns}
          data={regions}
          searchPlaceholder="Search ..."
          addButtonText="New Region"
          onView={handleView}
          actions={["view", "edit", "delete"]}
          loading={loading}
          loadingMore={loadingMore}
          loadMoreText="Load More Regions"
          onLoadMore={handleLoadMore}
          showLoadMore={true}
          highlightField="addedBy"
          highlightColors={{
            BUSINESS:
              "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
            PERSONAL:
              "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
          }}
          // Modal forms
          createForm={({ onClose }) => (
            <RegionForm onClose={onClose} onSubmit={handleCreateSubmit} />
          )}
          editForm={({ item, onClose }) => (
            <RegionForm
              item={item}
              onClose={onClose}
              onSubmit={handleEditSubmit}
            />
          )}
          deleteForm={({ item, onClose }) => (
            <DeleteConfirmation
              item={item}
              onClose={onClose}
              onConfirm={handleDeleteConfirm}
              itemName="Region"
            />
          )}
          createModalTitle="Create New Region"
          editModalTitle="Region"
          deleteModalTitle=""
          modalSize="lg"
          deleteModalSize="sm"
        />

      </div>

    </PrivateLayout>
  
  )
}

export default Regions

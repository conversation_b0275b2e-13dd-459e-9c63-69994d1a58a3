import instance from '../axios/instance.jsx';

// Follow-ups API endpoints
const ENDPOINTS = {
  FOLLOW_UPS: '/activities/followups',
  FOLLOW_UP_STATUS: (id) => `/activities/followups/${id}/status`,
};

// Follow-ups Service
export const followUpsService = {
  // Get all follow-ups
  getAll: async () => {
    try {
      console.log('=== FETCHING ALL FOLLOW-UPS ===');
      console.log(`API Endpoint: GET ${ENDPOINTS.FOLLOW_UPS}`);
      
      const response = await instance.get(ENDPOINTS.FOLLOW_UPS);
      
      console.log('Follow-ups response:', response.data);
      console.log('===============================');
      
      return response.data;
    } catch (error) {
      console.error('Error fetching follow-ups:', error);
      throw error;
    }
  },

  // Update follow-up status
  updateStatus: async (id, status) => {
    try {
      console.log('=== UPDATING FOLLOW-UP STATUS ===');
      console.log(`Follow-up ID: ${id}`);
      console.log(`New Status: ${status}`);
      console.log(`API Endpoint: POST ${ENDPOINTS.FOLLOW_UP_STATUS(id)}`);
      
      const response = await instance.post(ENDPOINTS.FOLLOW_UP_STATUS(id), {
        status: status
      });
      
      console.log('Status update response:', response.data);
      console.log('=================================');
      
      return response.data;
    } catch (error) {
      console.error('Error updating follow-up status:', error);
      throw error;
    }
  },


};

// Helper function to format date and time
const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

// Helper function to format follow-up status
const formatStatus = (status) => {
  if (!status) return 'Pending';
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
};

// Helper function to format follow-up type
const formatFollowUpType = (type) => {
  if (!type) return 'N/A';
  
  const typeMap = {
    'call': 'Phone Call',
    'visit': 'Site Visit',
    'phone_call': 'Phone Call',
    'site_visit': 'Site Visit'
  };
  
  return typeMap[type.toLowerCase()] || type;
};

// Data formatter function for follow-ups table
export const formatFollowUpsForTable = (apiResponse) => {
  console.log('=== FORMATTING FOLLOW-UPS FOR TABLE ===');
  console.log('Raw API response:', apiResponse);

  if (!apiResponse || !Array.isArray(apiResponse.data)) {
    console.warn('Invalid API response format for follow-ups');
    return [];
  }

  const formattedFollowUps = apiResponse.data.map((followUp, index) => {
    console.log(`=== FORMATTING FOLLOW-UP ${index + 1} ===`);
    console.log('Raw follow-up data:', followUp);
    console.log('followUp.lead_id:', followUp.lead_id);
    console.log('followUp.lead_name:', followUp.lead_name);

    const formatted = {
      id: followUp.id || `followup${index + 1}`,
      clientName: followUp.lead_name || "Unknown Client",
      anchorName: followUp.anchor_name || null,
      assignedOfficer: followUp.assigned_officer?.name || "N/A",
      assignedOfficerCode: followUp.assigned_officer?.rm_code || "N/A",
      followUpType: formatFollowUpType(followUp.followup_type),
      followUpReason: followUp.followup_reason || followUp.purpose?.name || "N/A",
      date: followUp.followup_date ? formatDateTime(followUp.followup_date) : "N/A",
      status: formatStatus(followUp.status),
      createdDate: followUp.created_date ? formatDateTime(followUp.created_date) : "N/A",
      notes: followUp.notes || "N/A",
      // Store original data for actions
      leadId: followUp.lead_id || followUp.leadId || followUp.leadID,
      purposeId: followUp.purpose?.id,
      originalData: followUp,
    };

    console.log('Formatted follow-up:', formatted);
    console.log('formatted.leadId:', formatted.leadId);
    console.log('=====================================');

    return formatted;
  });

  console.log('Formatted follow-ups for table:', formattedFollowUps);
  console.log('======================================');

  return formattedFollowUps;
};

// Extract summary statistics from API response
export const extractSummaryStats = (apiResponse) => {
  if (!apiResponse || !apiResponse.summary) {
    return {
      pending: 0,
      completed: 0,
      cancelled: 0,
      call_followups: 0,
      visit_followups: 0,
      total: 0
    };
  }

  return {
    pending: apiResponse.summary.pending || 0,
    completed: apiResponse.summary.completed || 0,
    cancelled: apiResponse.summary.cancelled || 0,
    call_followups: apiResponse.summary.call_followups || 0,
    visit_followups: apiResponse.summary.visit_followups || 0,
    total: apiResponse.total || 0
  };
};

export default followUpsService;

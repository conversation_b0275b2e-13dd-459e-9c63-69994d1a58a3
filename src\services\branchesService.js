import instance from '../axios/instance.jsx';

// Branches API endpoints
const ENDPOINTS = {
  BRANCHES: '/branches',
  BRANCH_BY_ID: (id) => `/branches/${id}`,
};

// Branches Service
export const branchesService = {
  // Get all branches
  getAll: async () => {
    try {
      const response = await instance.get(ENDPOINTS.BRANCHES);
      return response.data;
    } catch (error) {
      console.error('Error fetching branches:', error);
      throw error;
    }
  },

  // Create a new branch
  create: async (branchData) => {
    try {
      const response = await instance.post(ENDPOINTS.BRANCHES, branchData);
      return response.data;
    } catch (error) {
      console.error('Error creating branch:', error);
      throw error;
    }
  },

  // Update an existing branch
  update: async (id, branchData) => {
    try {
      const response = await instance.patch(ENDPOINTS.BRANCH_BY_ID(id), branchData);
      return response.data;
    } catch (error) {
      console.error('Error updating branch:', error);
      throw error;
    }
  },

  // Delete a branch
  delete: async (id) => {
    try {
      const response = await instance.delete(ENDPOINTS.BRANCH_BY_ID(id));
      return response.status === 204;
    } catch (error) {
      console.error('Error deleting branch:', error);
      throw error;
    }
  },

  // Get a single branch by ID
  getById: async (id) => {
    try {
      const response = await instance.get(ENDPOINTS.BRANCH_BY_ID(id));
      return response.data;
    } catch (error) {
      console.error('Error fetching branch by ID:', error);
      throw error;
    }
  },
};

// Data formatter functions
export const formatBranchData = (branch) => {
  return {
    id: branch.id,
    name: branch.name,
    code: branch.code || 'N/A',
    location: branch.location || 'N/A',
    address: branch.address || 'N/A',
    phoneNumber: branch.phoneNumber || 'N/A',
    email: branch.email || 'N/A',
    manager: branch.manager || 'N/A',
    addedOnDate: branch.addedOnDate,
    addedBy: branch.addedBy || 'N/A',
    // Format date for display
    formattedDate: formatDateTime(branch.addedOnDate),
  };
};

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';

  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

export const formatBranchesResponse = (response) => {
  if (!response || !response.data) {
    return [];
  }

  return response.data.map(formatBranchData);
};

export default branchesService;

import { useState, useEffect } from "react";
import { Info } from "lucide-react";
import Select from "react-select";
import {
  isicSectorsService,
  formatSectorsResponse,
} from "../../services/isicSectorsService";
import {
  customerCategoriesService,
  formatCategoriesResponse,
} from "../../services/customerCategoriesService";

import {
  branchesService,
  formatBranchesResponse,
} from "../../services/branchesService";
import { leadsService } from "../../services/leadsService";
import { anchorsService, formatAnchorsForDropdown } from "../../services/anchorsService";
import instance from "../../axios/instance.jsx";

//NOTE: if an item is passed in, it's an edit form
const LeadForm = ({
  item,
  onClose,
  onSubmit,
  initialAnchorRelationship,
  initialAnchor,
  initialCustomerCategory,
  initialIsicSector,
  initialBranch,
}) => {
  const [formData, setFormData] = useState({
    anchorId: "", // New field for anchor selection
    anchorRelationshipId: "", // New field for anchor relationship
    customerName: "",
    phoneNumber: "",
    customerCategoryId: "", // Changed to store ID instead of name
    isicSectorId: "", // Changed to store ID instead of name
    branchId: "", // Changed to store ID instead of name
    contactPersonName: "",
    contactPersonPhone: "",
    employerId: "", // Changed to store ID instead of name
    employerName: "", // New field for employer name
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [anchors, setAnchors] = useState([]);
  const [anchorsLoading, setAnchorsLoading] = useState(true);
  const [anchorRelationships, setAnchorRelationships] = useState([]);
  const [anchorRelationshipsLoading, setAnchorRelationshipsLoading] =
    useState(true);
  const [isicSectors, setIsicSectors] = useState([]);
  const [isicSectorsLoading, setIsicSectorsLoading] = useState(true);
  const [customerCategories, setCustomerCategories] = useState([]);
  const [customerCategoriesLoading, setCustomerCategoriesLoading] =
    useState(true);

  const [branches, setBranches] = useState([]);
  const [branchesLoading, setBranchesLoading] = useState(true);

  // Track if form has been initialized to prevent re-initialization
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  // Fetch all dropdown data on component mount
  useEffect(() => {
    fetchAnchors();
    fetchAnchorRelationships();
    fetchIsicSectors();
    fetchCustomerCategories();

    fetchBranches();
  }, []);

  const fetchAnchors = async () => {
    try {
      setAnchorsLoading(true);
      console.log("Fetching anchors from dedicated anchors API...");

      // Use the new dedicated anchors service
      const response = await anchorsService.getAll();

      // Convert to React Select format using the new formatter
      const anchorOptions = formatAnchorsForDropdown(response);

      console.log(`Successfully loaded ${anchorOptions.length} anchors`);
      setAnchors(anchorOptions);
    } catch (error) {
      console.error("Error fetching anchors:", error);

      // Fallback to leads service if anchors API is not available
      console.warn("Falling back to leads service for anchors...");
      try {
        const fallbackResponse = await leadsService.getAll();
        const fallbackOptions = fallbackResponse.data.map((lead) => {
          // Create label with format: {lead_name} - {account_id} (fallback case)
          const accountId = lead.account_id || lead.account_number || "No Account ID";
          const label = `${lead.lead_name} - ${accountId}`;

          return {
            value: lead.id,
            label: label,
            name: lead.lead_name, // Keep original name for reference
            accountId: accountId, // Keep account ID for reference
            email: lead.email || "No email",
            phoneNumber: lead.phoneNumber || "No phone",
            leadsCount: 0,
            isInUse: false,
          };
        });

        console.log(`Fallback: loaded ${fallbackOptions.length} anchors from leads`);
        setAnchors(fallbackOptions);
      } catch (fallbackError) {
        console.error("Error in fallback anchor fetching:", fallbackError);
        setAnchors([]);
      }
    } finally {
      setAnchorsLoading(false);
    }
  };

  const fetchAnchorRelationships = async () => {
    try {
      setAnchorRelationshipsLoading(true);
      // Make API call to fetch anchor relationships using axios instance
      const response = await instance.get("/anchor-relationships");
      console.log("Anchor relationships response:", response.data);

      // Convert to React Select format
      const relationshipOptions = response.data.data.map((relationship) => ({
        value: relationship.id,
        label: relationship.name,
        relationship: relationship,
      }));

      setAnchorRelationships(relationshipOptions);
      console.log("Processed anchor relationships:", relationshipOptions);
    } catch (error) {
      console.error("Error fetching anchor relationships:", error);
      setAnchorRelationships([]);
    } finally {
      setAnchorRelationshipsLoading(false);
    }
  };

  const fetchIsicSectors = async () => {
    try {
      setIsicSectorsLoading(true);
      const response = await isicSectorsService.getAll();
      const formattedSectors = formatSectorsResponse(response);

      // Convert to React Select format
      const sectorOptions = formattedSectors.map((sector) => ({
        value: sector.id, // Use ID as value
        label: `${sector.code ? `${sector.code} - ` : ""}${sector.name}`, // Show code and name
        sector: sector, // Keep full sector data for reference
      }));

      setIsicSectors(sectorOptions);
    } catch (error) {
      console.error("Error fetching ISIC sectors:", error);
      setIsicSectors([]); // Set empty array on error
    } finally {
      setIsicSectorsLoading(false);
    }
  };

  const fetchCustomerCategories = async () => {
    try {
      setCustomerCategoriesLoading(true);
      const response = await customerCategoriesService.getAll();
      const formattedCategories = formatCategoriesResponse(response);

      // Convert to React Select format
      const categoryOptions = formattedCategories.map((category) => ({
        value: category.id, // Use ID as value
        label: category.name,
        category: category, // Keep full category data for reference
      }));

      setCustomerCategories(categoryOptions);
    } catch (error) {
      console.error("Error fetching customer categories:", error);
      setCustomerCategories([]); // Set empty array on error
    } finally {
      setCustomerCategoriesLoading(false);
    }
  };

  const fetchBranches = async () => {
    try {
      setBranchesLoading(true);
      const response = await branchesService.getAll();
      const formattedBranches = formatBranchesResponse(response);

      // Convert to React Select format
      const branchOptions = formattedBranches.map((branch) => ({
        value: branch.id, // Use ID as value
        label: `${branch.name}`, // Show code and name
        branch: branch, // Keep full branch data for reference
      }));

      setBranches(branchOptions);
    } catch (error) {
      console.error("Error fetching branches:", error);
      setBranches([]); // Set empty array on error
    } finally {
      setBranchesLoading(false);
    }
  };

  // Populate form with lead data for editing using passed props or API fallback
  useEffect(() => {
    const populateFormData = async () => {
      if (!item || isFormInitialized) return;

      console.log("Populating form for editing with item:", item);
      console.log("Initial customer category:", initialCustomerCategory);
      console.log("Initial ISIC sector:", initialIsicSector);
      console.log("Initial branch:", initialBranch);
      console.log("Initial anchor relationship:", initialAnchorRelationship);
      console.log("Anchor ID from item:", item.parent_lead_id || item.anchorId);
      console.log(
        "Anchor Relationship ID from item:",
        item.anchor_relationship_id || item.anchorRelationshipId
      );
      console.log(
        "Anchor Relationship Name from item:",
        item.anchor_relationship_name
      );
      console.log("Parent Lead Name from item:", item.parent_lead_name);
      console.log("Employer Name from item:", item.employerName);

      // If we have the initial props for customer category, ISIC sector, or branch, use them directly (preferred path)
      if (initialCustomerCategory || initialIsicSector || initialBranch) {
        setFormData({
          anchorId: item.parent_lead_id || item.anchorId || "",
          anchorRelationshipId:
            (initialAnchorRelationship && initialAnchorRelationship.id) ||
            item.anchor_relationship_id ||
            item.anchorRelationshipId ||
            "",
          customerName: item.lead_name || item.name || "",
          phoneNumber: item.phoneNumber || "",
          customerCategoryId:
            (initialCustomerCategory && initialCustomerCategory.id) || "",
          isicSectorId: (initialIsicSector && initialIsicSector.id) || "",
          branchId: (initialBranch && initialBranch.id) || "",
          contactPersonName: item.contactPersonName || "",
          contactPersonPhone: item.contactPersonPhone || "",
          employerId: item.employerId || "",
          employerName: item.employerName || "",
        });
        setIsFormInitialized(true);
      } else if (item.id) {
        // Fallback: fetch lead details from API if initial props are not available
        try {
          console.log("Fetching lead details for editing:", item.id);
          const leadDetails = await leadsService.getDetails(item.id);
          console.log("Lead details fetched:", leadDetails);

          // Get first contact person if available
          const firstContactPerson =
            leadDetails.contact_persons &&
            leadDetails.contact_persons.length > 0
              ? leadDetails.contact_persons[0]
              : null;

          const anchorRelationshipId =
            (initialAnchorRelationship && initialAnchorRelationship.id) ||
            leadDetails.anchor_relationship_id ||
            leadDetails.anchorRelationshipId ||
            "";

          console.log("Setting anchor relationship ID:", anchorRelationshipId);
          console.log(
            "From initialAnchorRelationship:",
            initialAnchorRelationship
          );

          setFormData({
            anchorId: leadDetails.parent_lead_id || leadDetails.anchorId || "",
            anchorRelationshipId: anchorRelationshipId,
            customerName: leadDetails.customer_name || "",
            phoneNumber: leadDetails.phone_number || "",
            customerCategoryId: leadDetails.customer_category?.id || "",
            isicSectorId: leadDetails.isic_sector?.id || "",
            branchId: leadDetails.branch?.id || "",
            contactPersonName: firstContactPerson?.name || "",
            contactPersonPhone: firstContactPerson?.phone_number || "",
            employerId: leadDetails.employer?.id || "",
            employerName: item.employerName || leadDetails.employerName || "",
          });
          setIsFormInitialized(true);
        } catch (error) {
          console.error("Error fetching lead details:", error);
          // Final fallback to basic item data
          setFormData({
            anchorId: item.parent_lead_id || "",
            anchorRelationshipId:
              (initialAnchorRelationship && initialAnchorRelationship.id) ||
              item.anchor_relationship_id ||
              "",
            customerName: item.lead_name || item.name || "",
            phoneNumber: item.phoneNumber || "",
            customerCategoryId: "",
            isicSectorId: "",
            branchId: "",
            contactPersonName: item.contactPersonName || "",
            contactPersonPhone: item.contactPersonPhone || "",
            employerId: "",
            employerName: item.employerName || "",
          });
          setIsFormInitialized(true);
        }
      } else {
        // No API call needed, just use basic item data
        setFormData({
          anchorId: item.parent_lead_id || "",
          anchorRelationshipId:
            (initialAnchorRelationship && initialAnchorRelationship.id) ||
            item.anchor_relationship_id ||
            "",
          customerName: item.lead_name || item.name || "",
          phoneNumber: item.phoneNumber || "",
          customerCategoryId: "",
          isicSectorId: "",
          branchId: "",
          contactPersonName: item.contactPersonName || "",
          contactPersonPhone: item.contactPersonPhone || "",
          employerId: "",
          employerName: item.employerName || "",
        });
        setIsFormInitialized(true);
      }
    };

    populateFormData();
  }, [item?.id]); // Only depend on item.id to prevent re-initialization when user is editing

  // Reset initialization flag when item changes (new edit session)
  useEffect(() => {
    setIsFormInitialized(false);
  }, [item?.id]);

  // Custom styles for React Select
  const selectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: "48px",
      borderColor: state.isFocused
        ? "#10b981"
        : errors[state.selectProps.name]
        ? "#ef4444"
        : "#d1d5db",
      boxShadow: "none",
      "&:hover": {
        borderColor: state.isFocused ? "#10b981" : "#9ca3af",
      },
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? "#10b981"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
    }),
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle React Select changes
  const handleSelectChange = (selectedOption, actionMeta) => {
    const { name } = actionMeta;
    const value = selectedOption ? selectedOption.value : "";

    // Map field names to their corresponding ID fields in form data
    const fieldMappings = {
      anchor: "anchorId",
      anchorRelationship: "anchorRelationshipId",
      isicSector: "isicSectorId",
      customerCategory: "customerCategoryId",
      employer: "employerId",
      branch: "branchId",
    };

    const fieldName = fieldMappings[name] || name;

    // Special handling for anchor relationship changes
    if (name === "anchorRelationship" && selectedOption) {
      // If relationship is "Employee", auto-fill employer name with anchor name
      if (selectedOption.label === "Employer") {
        const selectedAnchor = anchors.find(
          (anchor) => anchor.value === formData.anchorId
        );
        if (selectedAnchor) {
          setFormData((prev) => ({
            ...prev,
            [fieldName]: value,
            employerName: selectedAnchor.name || selectedAnchor.label,
          }));
        } else {
          setFormData((prev) => ({
            ...prev,
            [fieldName]: value,
          }));
        }
      } else {
        // Clear employer name if relationship is not "Employee"
        setFormData((prev) => ({
          ...prev,
          [fieldName]: value,
          employerName: "",
        }));
      }
    } else if (name === "customerCategory") {
      // Special handling for customer category changes
      const selectedCategory = selectedOption;
      const requiresContactPerson =
        selectedCategory &&
        !["Employed", "Junior and youth", "Youth"].includes(
          selectedCategory.label
        );

      // Clear contact person fields if not required for this category
      if (!requiresContactPerson) {
        setFormData((prev) => ({
          ...prev,
          [fieldName]: value,
          contactPersonName: "",
          contactPersonPhone: "",
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [fieldName]: value,
        }));
      }
    } else {
      // Normal field update
      setFormData((prev) => ({
        ...prev,
        [fieldName]: value,
      }));
    }

    // Clear error when user makes selection
    if (errors[fieldName]) {
      setErrors((prev) => ({
        ...prev,
        [fieldName]: "",
        // Also clear contact person errors when category changes
        ...(name === "customerCategory" && {
          contactPersonName: "",
          contactPersonPhone: "",
        }),
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    console.log("=== FORM VALIDATION DEBUG ===");
    console.log("Form Data:", formData);
    console.log("Customer Categories:", customerCategories);
    console.log("Contact Person Name:", `"${formData.contactPersonName}"`);
    console.log("Contact Person Phone:", `"${formData.contactPersonPhone}"`);
    console.log(
      "Contact Person Phone Length:",
      formData.contactPersonPhone.length
    );

    // Required fields
    if (!formData.customerName.trim()) {
      newErrors.customerName = "Customer name is required";
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (
      !/^[\+]?[0-9\s\-\(\)]{7,15}$/.test(
        formData.phoneNumber.replace(/\s/g, "")
      )
    ) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    }

    if (!formData.branchId.trim()) {
      newErrors.branchId = "Branch is required";
    }

    if (!formData.customerCategoryId.trim()) {
      newErrors.customerCategoryId = "Customer category is required";
    }

    if (!formData.isicSectorId.trim()) {
      newErrors.isicSectorId = "ISIC sector is required";
    }

    // Get selected category for conditional validation
    const selectedCategory = customerCategories.find(
      (cat) => cat.value === formData.customerCategoryId
    );

    console.log("Selected Category:", selectedCategory);
    console.log("Should show employer name:", shouldShowEmployerName());
    console.log("Should show contact person:", shouldShowContactPerson());

    // Employer name required for employed customers
    if (
      selectedCategory?.label === "Employed" &&
      !formData.employerName.trim()
    ) {
      newErrors.employerName =
        "Employer name is required for employed customers";
    }

    // Contact person required for all categories except Employed, Junior and youth, and Youth
    const requiresContactPerson =
      selectedCategory &&
      !["Employed", "Junior and youth", "Youth"].includes(
        selectedCategory.label
      );

    console.log("Requires Contact Person:", requiresContactPerson);
    console.log(
      "Contact Person Phone Trim:",
      `"${formData.contactPersonPhone.trim()}"`
    );

    // Only validate contact person fields if they are required for this category
    if (requiresContactPerson) {
      if (!formData.contactPersonName.trim()) {
        newErrors.contactPersonName =
          "Contact person name is required for this category";
      }

      // Contact person phone validation - only validate if phone has content
      if (formData.contactPersonPhone.trim()) {
        const phoneValue = formData.contactPersonPhone.trim();
        // Use the same validation as main phone number
        if (!/^[\+]?[0-9\s\-\(\)]{7,15}$/.test(phoneValue.replace(/\s/g, ""))) {
          newErrors.contactPersonPhone = "Please enter a valid phone number";
        }
      }
    }

    // Explicitly log what we're doing with contact person validation
    console.log("Contact person validation applied:", requiresContactPerson);
    console.log("Contact person errors:", {
      name: newErrors.contactPersonName,
      phone: newErrors.contactPersonPhone,
    });

    console.log("Validation Errors:", newErrors);
    console.log("Validation Result:", Object.keys(newErrors).length === 0);
    console.log("==============================");

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper functions for conditional rendering
  const shouldShowEmployerName = () => {
    const selectedCategory = customerCategories.find(
      (cat) => cat.value === formData.customerCategoryId
    );
    return selectedCategory?.label === "Employed";
  };

  const shouldShowContactPerson = () => {
    const selectedCategory = customerCategories.find(
      (cat) => cat.value === formData.customerCategoryId
    );
    return (
      selectedCategory &&
      !["Employed", "Junior and youth", "Youth"].includes(
        selectedCategory.label
      )
    );
  };

  // Reset form to initial state
  const handleReset = () => {
    setFormData({
      anchorId: "",
      anchorRelationshipId: "",
      customerName: "",
      phoneNumber: "",
      customerCategoryId: "",
      isicSectorId: "",
      branchId: "",
      contactPersonName: "",
      contactPersonPhone: "",
      employerId: "",
      employerName: "",
    });
    setErrors({});
  };

  const handleSubmit = async (e) => {
    console.log("=== FORM SUBMIT TRIGGERED ===");
    console.log("Event:", e);
    e.preventDefault();

    console.log("Running form validation...");
    const isValid = validateForm();
    console.log("Form validation result:", isValid);

    if (!isValid) {
      console.log("Form validation failed, stopping submission");
      return;
    }

    console.log("Form is valid, proceeding with submission...");
    setIsSubmitting(true);

    try {
      // Prepare data for API
      const baseLeadData = {
        branchId: formData.branchId,
        contactPersonName: formData.contactPersonName,
        contactPersonPhone: formData.contactPersonPhone,
        customerCategoryId: formData.customerCategoryId,
        customerName: formData.customerName,
        employerId: formData.employerId,
        employerName: formData.employerName,
        isicSectorId: formData.isicSectorId,
        phoneNumber: formData.phoneNumber,
      };

      // Only include anchor fields if they have values
      if (formData.anchorId && formData.anchorId.trim()) {
        baseLeadData.anchorId = formData.anchorId;
      }

      if (
        formData.anchorRelationshipId &&
        formData.anchorRelationshipId.trim()
      ) {
        baseLeadData.anchorRelationshipId = formData.anchorRelationshipId;
      }

      // Add createdDate only for new leads (create mode)
      const leadData = item
        ? baseLeadData // Edit mode: exclude createdDate
        : { ...baseLeadData, createdDate: new Date().toISOString() }; // Create mode: include createdDate

      console.log("=== FORM SUBMISSION DEBUG ===");
      console.log("Form Data State:", formData);
      console.log("Base Lead Data:", baseLeadData);
      console.log("Final Lead Data being submitted:", leadData);
      console.log("Is Edit Mode:", !!item);
      console.log("==============================");

      if (item) {
        // Edit mode - update existing lead
        const updateData = {
          ...leadData,
          // id: item.id
        };

        console.log(
          `Making PATCH request to /leads/${item.id} with data:`,
          updateData
        );
        const result = await leadsService.update(item.id, updateData);
        console.log("Lead updated successfully (200 response):", result);

        // Transform API response to match table format (same as create)
        const formatStatus = (status) => {
          if (!status) return "Pending";
          return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
        };

        const updatedLeadForTable = {
          id: result.id || item.id,
          anchor: result.anchor_name || item.anchor,
          name: result.lead_name || formData.customerName,
          phoneNumber: result.phoneNumber || formData.phoneNumber,
          visits: result.no_of_visits || item.visits,
          calls: result.no_of_calls || item.calls,
          lastInteraction: result.last_interaction || item.lastInteraction,
          lastInteractionType: result.last_interaction
            ? "call"
            : item.lastInteractionType,
          status: formatStatus(result.status) || item.status,
          // Additional fields for form compatibility
          category: "Employed", // Default - could be enhanced later
          branchName: "N/A", // Could be enhanced with branch lookup
          type: "New",
          isicSector: "N/A", // Could be enhanced with sector lookup
          clientId: formData.clientId,
          contactPersonName: formData.contactPersonName,
          contactPersonPhone: formData.contactPersonPhone,
          employerName: "N/A", // Could be enhanced with employer lookup
          // Profile specific fields
          email: item.email || "N/A",
          age: item.age || "N/A",
          gender: item.gender || "N/A",
          region: item.region || "N/A",
          hitListGroup: item.hitListGroup || "N/A",
        };

        await onSubmit(updatedLeadForTable, item);
      } else {
        // Create mode - create new lead
        console.log("Making POST request to /leads with data:", leadData);
        const result = await leadsService.create(leadData);
        console.log("Lead created successfully:", result);

        // Helper function to capitalize status to match existing color definitions
        const formatStatus = (status) => {
          if (!status) return "Pending";
          return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
        };

        // Transform API response to match table format
        const newLeadForTable = {
          id: result.id,
          anchor: result.anchor_name,
          name: result.lead_name,
          phoneNumber: result.phoneNumber,
          visits: result.no_of_visits,
          calls: result.no_of_calls,
          lastInteraction: result.last_interaction,
          lastInteractionType: result.last_interaction ? "call" : null, // Default assumption
          status: formatStatus(result.status), // Capitalize to match existing color definitions
          // Additional fields for form compatibility
          category: "Employed", // Default - could be enhanced later
          branchName: "N/A", // Could be enhanced with branch lookup
          type: "New",
          isicSector: "N/A", // Could be enhanced with sector lookup
          clientId: "",
          contactPersonName: formData.contactPersonName,
          contactPersonPhone: formData.contactPersonPhone,
          employerName: "N/A", // Could be enhanced with employer lookup
          // Profile specific fields
          email: "N/A",
          age: "N/A",
          gender: "N/A",
          region: "N/A",
          hitListGroup: "N/A",
        };

        await onSubmit(newLeadForTable);
      }

      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
      // Handle error (show notification, etc.)
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-h-[80vh] overflow-y-auto max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-8 p-8">
        {/* Section 1: Anchor Information */}
        <div className="space-y-6">


          {/* Anchor Name and Lead's Role with Anchor - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
            {/* Anchor Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Anchor Name
              </label>
              <Select
                name="anchor"
                value={anchors.find(
                  (option) => option.value === formData.anchorId
                )}
                onChange={handleSelectChange}
                options={anchors}
                styles={selectStyles}
                placeholder={
                  anchorsLoading ? "Loading anchors..." : "Select anchor lead"
                }
                isSearchable
                isLoading={anchorsLoading}
                isDisabled={anchorsLoading}
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {errors.anchorId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.anchorId}
                </p>
              )}
            </div>

            {/* Lead's Role with Anchor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                 Lead Relationship with Anchor
              </label>
              <Select
                name="anchorRelationship"
                value={anchorRelationships.find(
                  (option) => option.value === formData.anchorRelationshipId
                )}
                onChange={handleSelectChange}
                options={anchorRelationships}
                styles={selectStyles}
                placeholder={
                  anchorRelationshipsLoading
                    ? "Loading relationships..."
                    : "Select relationship"
                }
                isSearchable
                isLoading={anchorRelationshipsLoading}
                isDisabled={anchorRelationshipsLoading}
                className="react-select-container"
                classNamePrefix="react-select"
              />
              {errors.anchorRelationshipId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.anchorRelationshipId}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Section 2: Lead Information */}
        <div className="space-y-6">


          {/* Customer Name and Phone Number - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
            {/* Customer Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Lead Name *
              </label>
              <input
                type="text"
                name="customerName"
                value={formData.customerName}
                onChange={handleChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                  errors.customerName
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="Enter customer full name"
              />
              {errors.customerName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.customerName}
                </p>
              )}
            </div>

            {/* Phone Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Phone Number *
              </label>
              <input
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                  errors.phoneNumber
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="e.g., +254712345678 or 0712345678"
              />
              {errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.phoneNumber}
                </p>
              )}
            </div>
          </div>

          {/* Customer Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Lead Category *
            </label>
            <Select
              name="customerCategory"
              value={customerCategories.find(
                (option) => option.value === formData.customerCategoryId
              )}
              onChange={handleSelectChange}
              options={customerCategories}
              styles={selectStyles}
              placeholder={
                customerCategoriesLoading
                  ? "Loading categories..."
                  : "Select customer category"
              }
              isSearchable
              isLoading={customerCategoriesLoading}
              isDisabled={customerCategoriesLoading}
              className="react-select-container"
              classNamePrefix="react-select"
            />
            {errors.customerCategoryId && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.customerCategoryId}
              </p>
            )}
          </div>

          {/* ISIC Sector */}
          <div>
            <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              ISIC Sector *
              <div className="group relative ml-2">
                <Info size={16} className="text-gray-400 cursor-help" />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  International Standard Industrial Classification
                </div>
              </div>
            </label>
            <Select
              name="isicSector"
              value={isicSectors.find(
                (option) => option.value === formData.isicSectorId
              )}
              onChange={handleSelectChange}
              options={isicSectors}
              styles={selectStyles}
              placeholder={
                isicSectorsLoading
                  ? "Loading sectors..."
                  : "Select industry sector"
              }
              isSearchable
              isLoading={isicSectorsLoading}
              isDisabled={isicSectorsLoading}
              className="react-select-container"
              classNamePrefix="react-select"
            />
            {errors.isicSectorId && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.isicSectorId}
              </p>
            )}
          </div>
        </div>

        {/* Section 2: Branch & Relationship Assignment */}
        <div className="space-y-6">
          

          {/* Branch Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Branch Name *
            </label>
            <Select
              name="branch"
              value={branches.find(
                (option) => option.value === formData.branchId
              )}
              onChange={handleSelectChange}
              options={branches}
              styles={selectStyles}
              placeholder={
                branchesLoading ? "Loading branches..." : "Select a branch"
              }
              isSearchable
              isLoading={branchesLoading}
              isDisabled={branchesLoading}
              className="react-select-container"
              classNamePrefix="react-select"
            />
            {errors.branchId && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.branchId}
              </p>
            )}
          </div>
        </div>

        {/* Section 3: Contact Person Details - Conditional */}
        {shouldShowContactPerson() && (
          <div className="space-y-6">
            

            {/* Contact Person Name and Phone - Side by Side */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contact Person Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contact Person Name *
                </label>
                <input
                  type="text"
                  name="contactPersonName"
                  value={formData.contactPersonName}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                    errors.contactPersonName
                      ? "border-red-500 dark:border-red-400"
                      : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                  }`}
                  placeholder="Enter contact person full name"
                />
                {errors.contactPersonName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.contactPersonName}
                  </p>
                )}
              </div>

              {/* Contact Person Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contact Person Phone No
                </label>
                <input
                  type="tel"
                  name="contactPersonPhone"
                  value={formData.contactPersonPhone}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 focus:border-green-500 hover:border-gray-400"
                  placeholder="e.g., +254712345678 or 0712345678"
                />
              </div>
            </div>
          </div>
        )}

        {/* Section 4: Employer Information - Conditional for Employed Category */}
        {shouldShowEmployerName() && (
          <div className="space-y-6">
            

            {/* Employer Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Employer Name *
              </label>
              <input
                type="text"
                name="employerName"
                value={formData.employerName}
                onChange={handleChange}
                className={`w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none transition-colors duration-200 ${
                  errors.employerName
                    ? "border-red-500 dark:border-red-400"
                    : "border-gray-300 dark:border-gray-600 focus:border-green-500 hover:border-gray-400"
                }`}
                placeholder="Enter employer name"
              />
              {errors.employerName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.employerName}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-between items-center pt-8 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={handleReset}
            className="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            Reset
          </button>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-3 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed rounded-lg transition-colors duration-200 flex items-center"
            >
              {isSubmitting && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              )}
              {isSubmitting
                ? "Processing..."
                : item
                ? "Update Lead"
                : "Create Lead"}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default LeadForm;

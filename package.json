{"name": "kb-bank-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@flaticon/flaticon-uicons": "^3.3.1", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.9.1", "@tailwindcss/postcss": "^4.1.11", "apexcharts": "^5.3.0", "axios": "^1.11.0", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "react-select": "^5.10.2", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.11", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}